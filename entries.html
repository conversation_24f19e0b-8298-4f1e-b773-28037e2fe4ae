<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Entries - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-list me-2"></i>Guest Entries</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <a href="add-entry.html" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Add Entry
              </a>
            </div>
          </div>

          <!-- Search Section -->
          <div class="container">
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="search-number">Search by CNIC or Mobile:</label>
                  <input type="text" id="search-number" class="form-control" placeholder="Enter CNIC or Mobile number" onkeyup="searchEntries()">
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="status-filter">Filter by Status:</label>
                  <select id="status-filter" class="form-control" onchange="filterByStatus()">
                    <option value="">All Status</option>
                    <option value="checked_in">Checked In</option>
                    <option value="checked_out">Checked Out</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Entries Table -->
            <div class="table-container">
              <div class="table-headerentry">
                <div>DATE</div>
                <div>NAME</div>
                <div>F-NAME</div>
                <div>CNIC</div>
                <div>MOBILE</div>
                <div>CAST</div>
                <div>PROFESSION</div>
                <div>CITY</div>
                <div>PROVINCE</div>
                <div>ROOM</div>
                <div>ASSIGNED BED</div>
                <div>CHECK IN</div>
                <div>CHECK OUT</div>
                <div>STATUS</div>
              </div>

              <div id="entriesTableBody">
                <!-- Entries will be loaded dynamically -->
              </div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    let allEntries = [];

    // Initialize the entries page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadEntries();
    });

    function loadEntries() {
      allEntries = dataManager.getAll('entries');
      displayEntries(allEntries);
    }

    function displayEntries(entries) {
      const container = document.getElementById('entriesTableBody');

      if (container) {
        container.innerHTML = '';
        entries.forEach(entry => {
          const row = document.createElement('div');
          row.className = 'table-rowentry';
          row.innerHTML = `
            <div data-label="DATE">${new Date(entry.created_at).toLocaleDateString()}</div>
            <div data-label="NAME">${entry.name}</div>
            <div data-label="F-NAME">${entry.father_name}</div>
            <div data-label="CNIC">${entry.cnic_no}</div>
            <div data-label="MOBILE">${entry.mobile_no}</div>
            <div data-label="CAST">${entry.cast}</div>
            <div data-label="PROFESSION">${entry.profession}</div>
            <div data-label="CITY">${entry.city_name}</div>
            <div data-label="PROVINCE">${entry.province_name}</div>
            <div data-label="ROOM">${entry.room_name}</div>
            <div data-label="ASSIGNED BED">${entry.bed_name || 'family'}</div>
            <div data-label="CHECK IN">${entry.check_in}</div>
            <div data-label="CHECK OUT">
              ${entry.check_out ? entry.check_out :
                `<button class="delete-btn checkOutBtn" onclick="checkOut(${entry.id})" style="padding: 6px 12px;">Check Out</button>`}
            </div>
            <div data-label="STATUS">
              <span class="badge ${entry.status === 'checked_in' ? 'bg-success' : 'bg-secondary'}">
                ${entry.status}
              </span>
            </div>
          `;
          container.appendChild(row);
        });
      }
    }

    function searchEntries() {
      const searchTerm = document.getElementById('search-number').value.toLowerCase();
      const statusFilter = document.getElementById('status-filter').value;

      let filteredEntries = allEntries;

      // Filter by search term
      if (searchTerm) {
        filteredEntries = filteredEntries.filter(entry =>
          entry.cnic_no.toLowerCase().includes(searchTerm) ||
          entry.mobile_no.toLowerCase().includes(searchTerm) ||
          entry.name.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by status
      if (statusFilter) {
        filteredEntries = filteredEntries.filter(entry => entry.status === statusFilter);
      }

      displayEntries(filteredEntries);
    }

    function filterByStatus() {
      searchEntries(); // Reuse the search function which handles both filters
    }

    function checkOut(entryId) {
      if (confirm('Are you sure you want to check out?')) {
        const now = new Date().toLocaleString();
        dataManager.update('entries', entryId, {
          check_out: now,
          status: 'checked_out'
        });

        // Update bed status to available
        const entry = dataManager.getById('entries', entryId);
        if (entry && entry.bed_ids) {
          const bedIds = entry.bed_ids.split(',');
          bedIds.forEach(bedId => {
            dataManager.update('beds', parseInt(bedId), { status: 'available' });
          });
        }

        loadEntries();
        alert('Checked out successfully!');
      }
    }
  </script>
</body>
</html>
