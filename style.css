/* Browser-like top navigation bar */
.browser-navbar {
  background-color: #1e2a31;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 40px;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1060;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.browser-navbar .left-section {
  display: flex;
  align-items: center;
}

.browser-navbar .back-button {
  color: #8aa4af;
  margin-right: 10px;
  font-size: 18px;
  text-decoration: none;
}

.browser-navbar .refresh-button {
  color: #8aa4af;
  margin-right: 10px;
  font-size: 18px;
}

.browser-navbar .url-bar {
  background-color: #2c3b44;
  color: #8aa4af;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 12px;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.browser-navbar .right-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.browser-navbar .right-section i {
  color: #8aa4af;
  font-size: 16px;
}

/* Company bar below browser bar */
.company-navbar {
  background-color: #2c3b44;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 30px;
  width: 100%;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 1055;
}

.company-navbar .company-info {
  display: flex;
  align-items: center;
}

.company-navbar .company-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 10px;
}

.company-navbar .company-name {
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.company-navbar .page-info {
  display: flex;
  align-items: center;
}

.company-navbar .page-title {
  font-size: 14px;
  color: #8aa4af;
  margin-left: 10px;
}

/* Adjust secondary navbar position */
.secondary-navbar {
  top: 70px;
  z-index: 1050;
}

/* Adjust main content and sidebar to account for all top bars */
body {
padding-top: 20px;
}

.sidebar {
  top: 120px;
  min-height: calc(100vh - 120px);
}

main {
  margin-top: 120px;
  padding-top: 0;
}

.container-fluid {
  padding-top: 0;
  margin-top: 0;
}

main {
  margin-top: 120px;
  padding-top: 0;
}

.page-section {
  padding-top: 10px;
}

#dashboard .container {
  margin-top: 0;
  padding-top: 0;
}

.d-flex.justify-content-between.align-items-center.mb-3 {
  margin-top: 0;
  margin-bottom: 10px;
}

/* Adjust the toggle sidebar button position */
.btn-toggle-sidebar {
  top: 130px;
}
  .sidebar {
    min-height: 100vh;
    background-color: #4CAF50;
    position: fixed;
    top: 30px;
    left: 0;
    width: 200px;
    transition: left 0.3s ease;
    z-index: 9999999 !important;
    padding: 0;
    box-shadow: 10px 0 13px -2px rgba(0, 0, 0, 0.3);
  }
  .sidebar-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20px;
    color: #fff;
    background-color: green;
  }
  .sidebar-header img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
    margin-right: 10px;
  }
  .sidebar-header .user-info {
    display: flex;
    flex-direction: column;
  }
  .sidebar-header .user-name {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 2px;
    text-transform: uppercase;
  }
  .sidebar-header .user-role {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 5px;
  }
  .sidebar-header .settings-link {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color 0.2s ease;
  }
  .sidebar-header .settings-link:hover {
    color: white;
    text-decoration: underline;
  }
  .sidebar-header .settings-link i {
    font-size: 0.75rem;
  }
  .sidebar-header .user-role::before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #4ade4a;
    border-radius: 50%;
    margin-right: 5px;
  }
  .sidebar h3 {
    color: #fff;
    padding: 20px;
    font-size: 1.5rem;
    font-weight: bold;
  }
  .sidebar .nav-link {
    font-size: 1.1rem;
    padding: 12px 10px;
    color: #fff;
  }
  .sidebar .nav-link:hover ::after {

    background-color: #3e8e41;
    border-radius: 0.25rem;
  
  }
  main {
    margin-left: 250px;
    transition: margin-left 0.3s ease;
  }
  .page-section {
    display: none;
    animation: fadeIn 0.5s;
    background: #fff;
    padding: 20px;
    border-radius: 0.5rem;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
  }
  .page-section.active {
    display: block;
  }
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  .btn-toggle-sidebar {
    display: none;
   
    z-index: 1100;
    font-size: 24px;
    width: 40px;
    height: 40px;
    
    color: #000000;
    border: none;
    border-radius: 5px;
    text-align: center;
    line-height: 40px;
  }
  .close-btn {
    display: none;
    background: transparent;
    color: #fff;
    border: none;
    font-size: 24px;
    position: absolute;
        top: -10px;
    right: -1px;
    z-index: 1010;
  }
  .sidebar.active .close-btn {
    display: block;
  }
  @media (max-width: 767px) {
    .sidebar {
      left: -250px;
    }
    .sidebar.active {
      left: 0;
    }
    .btn-toggle-sidebar {
      display: block;
    }
    main {
      margin-left: 0;
    }
  }
  /* Dashboard Styles */
  .card-custom {
    text-align: center;
    padding: 20px;
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  .card-custom:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  }
  .card-custom i {
    font-size: 28px;
    margin-bottom: 10px;
  }
  .card-custom h5 {
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 500;
  }
  .card-custom h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
  }
  .card-individual { background-color: #e6f4ea; }
  .card-family { background-color: #e6f4ea; }
  .card-rooms { background-color: #fce4ec; }
  .card-staff { background-color: #fff3cd; }
  .chart-container {
    height: 360px;
    width: 100%;
  }
  .chart-card {
    height: 460px;
    display: flex;
    flex-direction: column;
  }
  .chart-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .row-eq-height {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  .row-eq-height > div {
    flex: 1;
    display: flex;
  }
  @media (max-width: 991px) {
    .row-eq-height > div {
      flex: 0 0 48%;
      max-width: 48%;
    }
  }
  @media (max-width: 767px) {
    .row-eq-height > div {
      flex: 0 0 100%;
      max-width: 100%;
    }
    .card-custom, .chart-card {
      height: auto;
      margin: 10px 0;
    }
  }
  /* Room Card Styles */
  .room-card {
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
  }
  .room-card h5 {
    margin: 15px 0 10px;
    font-size: 1.2rem;
    font-weight: 500;
  }
  /* Table Styles */
  .nav-tabs {
    border-bottom: 1px solid #dee2e6;
  }
  .nav-tabs .nav-link {
    color: #495057;
    border: none;
    border-bottom: 2px solid transparent;
    margin-right: 10px;
  }
  .nav-tabs .nav-link.active {
    color: #4CAF50;
    border-bottom: 2px solid #4CAF50;
  }
  .table {
    margin-top: 10px;
  }
  .table th {
    background-color: #f8f9fa;
    font-weight: 500;
  }
  .table td {
    vertical-align: middle;
  }
  .status-due {
    color: #dc3545;
    background-color: #f8d7da;
    border-radius: 15px;
    padding: 5px 10px;
    display: inline-block;
  }
  .status-paid {
    color: #4CAF50;
    background-color: #d4edda;
    border-radius: 15px;
    padding: 5px 10px;
    display: inline-block;
  }
  .action-buttons .btn-add-sell {
    background-color: #4CAF50;
    border: none;
    color: #fff;
  }
  .action-buttons .btn-sell-list {
    background-color: #28a745;
    border: none;
    color: #fff;
  }
  /* General Table Styles */
  .table-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-x: auto;
    min-width: 100%;
    max-width: 100%;
  }
  .table-container::-webkit-scrollbar {
    height: 8px;
  }
  .table-container::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
  }
  .table-container::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
  .table-header, .table-headeruser, .table-headerpro, .table-headercity, .table-headerbed, .table-headers, .table-headerentry {
    display: grid;
    padding: 12px 20px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
    font-size: 13px;
    font-weight: 500;
    color: #4c5b4d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    gap: 10px;
  }
  .table-row, .table-rowuser, .table-rowspro, .table-rowcity, .table-rowbed, .table-rowes, .table-rowentry {
    display: grid;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%);
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    transition: transform 0.2s, box-shadow 0.2s;
    gap: 10px;
  }
  .table-row:hover, .table-rowuser:hover, .table-rowspro:hover, .table-rowcity:hover, .table-rowbed:hover, .table-rowes:hover, .table-rowentry:hover {
   background-color: rgba(244, 244, 244, 0.696);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
  .table-row div, .table-rowuser div, .table-rowspro div, .table-rowcity div, .table-rowbed div, .table-rowes div, .table-rowentry div {
    color: #202124;
    font-size: 14px;
    padding: 5px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  /* Adjusted column widths for each table */
  .table-headerentry, .table-rowentry {
    grid-template-columns: 1.5fr 1fr 1fr 1.2fr 1.2fr 0.8fr 1.2fr 1fr 1fr 0.8fr 0.8fr 0.8fr 1fr 0.6fr;
    min-width: 1400px;
  }
  .table-headeruser, .table-rowuser {
    grid-template-columns: 1.5fr 2fr 1fr 1.5fr;
    min-width: 600px;
  }
  .table-headerpro, .table-rowspro {
    grid-template-columns: 0.5fr 2fr 1fr;
    min-width: 400px;
  }
  .table-headercity, .table-rowcity {
    grid-template-columns: 0.5fr 1.5fr 1.5fr 1fr;
    min-width: 500px;
  }
  .table-headerbed, .table-rowbed {
    grid-template-columns: 0.5fr 1fr 1fr 1fr;
    min-width: 450px;
  }
  .table-headers, .table-rowes {
    grid-template-columns: 0.5fr 1fr 0.5fr 0.5fr 1fr;
    min-width: 500px;
  }
  /* Form Styles */
  .form-section {
    flex: 1;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
    padding: 20px;
  }
  .form-group {
    margin-bottom: 20px;
  }
  .form-group label {
    display: block;
    font-size: 14px;
    color: #5f6368;
    margin-bottom: 8px;
  }
  .form-group label span {
    color: #d93025;
  }
  .form-group input, .form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #e8eaed;
    border-radius: 5px;
    font-size: 14px;
    color: #202124;
  }
  .form-group select {
    appearance: none;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCA1NiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzVmNjM2OCIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat right 10px center;
    background-size: 12px;
  }
  .form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #1a73e8;
  }
  .submit-btn {
    background-color: #4CAF50;
    color: #ffffff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s, transform 0.1s;
  }
  .submit-btn:hover {
    background-color: #3e8e41;
    transform: translateY(-1px);
  }
  .submit-btn:active {
    transform: translateY(0);
  }
  /* Button Styles */
  .add-btn, .edit-btn, .delete-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: background-color 0.3s, transform 0.1s;
    margin: 5px;
  }
  .add-btn {
    background-color: #4CAF50;
    color: #ffffff;
  }
  .add-btn:hover {
    background-color: #3e8e41;
    transform: translateY(-1px);
  }
  .edit-btn {
    background-color: #4CAF50;
    color: #ffffff;
  }
  .edit-btn:hover {
    background-color: #3e8e41;
    transform: translateY(-1px);
  }
  .delete-btn {
    background-color: #d93025;
    color: #ffffff;
  }
  .delete-btn:hover {
    background-color: #b0281a;
    transform: translateY(-1px);
  }
  .add-btn:active, .edit-btn:active, .delete-btn:active {
    transform: translateY(0);
  }
  /* Header and Footer */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
  }
  .header h2 {
    font-size: 26px;
    color: #202124;
    font-weight: 600;
  }
  .footer {
    text-align: center;
    padding: 20px;
    color: #5f6368;
    font-size: 13px;
  }
  /* Avatar Styles */
  .avatar {
    width: 36px;
    height: 36px;
    max-width: 36px;
    max-height: 36px;
    border-radius: 50%;
    margin-right: 12px;
    vertical-align: middle;
    object-fit: cover;
  }
  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 30px;
    }
    .header h2 {
      font-size: 24px;
    }
    .add-btn, .edit-btn, .delete-btn {
      padding: 6px 12px;
      font-size: 12px;
    }
    .table-header, .table-headeruser, .table-headerpro, .table-headercity, .table-headerbed, .table-headers, .table-headerentry {
      font-size: 12px;
      padding: 10px 15px;
    }
    .table-row, .table-rowuser, .table-rowspro, .table-rowcity, .table-rowbed, .table-rowes, .table-rowentry {
      padding: 12px 15px;
    }
    .table-row div, .table-rowuser div, .table-rowspro div, .table-rowcity div, .table-rowbed div, .table-rowes div, .table-rowentry div {
      font-size: 13px;
    }
    .avatar {
      width: 32px;
      height: 32px;
      max-width: 32px;
      max-height: 32px;
    }
  }
  @media (max-width: 768px) {
    .container {
      padding: 20px;
    }
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
    .header h2 {
      font-size: 22px;
    }
    .add-btn {
      width: 100%;
      padding: 10px;
    }
    .table-header, .table-headeruser, .table-headerpro, .table-headercity, .table-headerbed, .table-headers, .table-headerentry {
      display: none;
    }
    .table-row, .table-rowuser, .table-rowspro, .table-rowcity, .table-rowbed, .table-rowes, .table-rowentry {
      
     
      align-items: flex-start;
      gap: 12px;
      padding: 15px;
     
    }
    .table-row div, .table-rowuser div, .table-rowspro div, .table-rowcity div, .table-rowbed div, .table-rowes div, .table-rowentry div {
      width: 100%;
     
      display: flex;
      flex-direction: column;
    }
    .table-row div::before, .table-rowuser div::before, .table-rowspro div::before, .table-rowcity div::before, .table-rowbed div::before, .table-rowes div::before, .table-rowentry div::before {
      content: attr(data-label);
      font-size: 12px;
      font-weight: 600;
      color: #5f6368;
      text-transform: uppercase;
      margin-bottom: 5px;
      padding-bottom: 2px;
      border-bottom: 1px solid #e8eaed;
    }
    .table-rowentry div button.delete-btn {
      width: 100%;
      text-align: center;
      padding: 1px;
      font-size: 14px;
    }
    .actions {
      flex-direction: row;
      justify-content: flex-start;
      width: 100%;
      gap: 10px;
    }
    .edit-btn, .delete-btn {
      flex: 1;
      text-align: center;
    }
    .form-section h3 {
      font-size: 16px;
    }
    .avatar {
      width: 30px;
      height: 30px;
      max-width: 30px;
      max-height: 30px;
    }
  }
  @media (max-width: 480px) {
    .container {
      padding: 15px;
    }
    .header h2 {
      font-size: 20px;
    }
    .add-btn, .edit-btn, .delete-btn {
      padding: 8px;
      font-size: 12px;
    }
    .table-row, .table-rowuser, .table-rowspro, .table-rowcity, .table-rowbed, .table-rowes, .table-rowentry {
      padding: 12px;
      gap: 10px;
    }
    .table-row div, .table-rowuser div, .table-rowspro div, .table-rowcity div, .table-rowbed div, .table-rowes div, .table-rowentry div {
      font-size: 13px;
    }
    .table-row div::before, .table-rowuser div::before, .table-rowspro div::before, .table-rowcity div::before, .table-rowbed div::before, .table-rowes div::before, .table-rowentry div::before {
      font-size: 11px;
    }
    .table-rowentry div button.delete-btn {
      padding: 1px;
      font-size: 12px;
    }
    .footer {
      font-size: 12px;
      padding: 15px;
    }
    .avatar {
      width: 28px;
      height: 28px;
      max-width: 28px;
      max-height: 28px;
    }
  }
  .nav-link .arrow {
    transition: transform 0.3s ease;
    display: inline-block;
    margin-left: 5px;
  }

  .nav-link[aria-expanded="true"] .arrow {
    transform: rotate(180deg);
  }

  .nav-link[aria-expanded="false"] .arrow {
    transform: rotate(0deg);
  }
  /* Dashboard Cards - Improved */
  .miancards {
    width: 100%;
    margin-bottom: 30px;
  }

  .dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    width: 100%;
  }

  .miancards .card {
    position: relative;
    border-radius: 6px;
    padding: 20px;
    color: white;
    text-align: left;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 160px;
    border: none;
  }

  .miancards .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  }

  .miancards .card h3 {
    font-size: 14px;
    margin: 0;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
  }

  .miancards .card .number {
    font-size: 36px;
    font-weight: bold;
    margin: 10px 0;
    line-height: 1;
  }

  .miancards .card p {
    font-size: 12px;
    margin: 5px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .miancards .card .today {
    font-size: 24px;
    font-weight: bold;
    margin-top: 5px;
    line-height: 1;
  }

  .miancards .card .icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 70px;
    opacity: 0.3;
  }

  .miancards .card .details {
    position: absolute;
    bottom: 15px;
    right: 15px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 4px 10px;
    border-radius: 4px;
  }

  .miancards .card .details:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
  }

  .miancards .card .details::after {
    content: "→";
    margin-left: 5px;
  }

  /* Card background patterns */
  .miancards .card::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: 0;
    background-repeat: no-repeat;
    background-position: right top;
  }

  /* Animation for the cards */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .miancards .card {
    animation: fadeInUp 0.5s ease forwards;
  }

  .miancards .card:nth-child(1) {
    animation-delay: 0.1s;
  }

  .miancards .card:nth-child(2) {
    animation-delay: 0.2s;
  }

  .miancards .card:nth-child(3) {
    animation-delay: 0.3s;
  }

  .miancards .card:nth-child(4) {
    animation-delay: 0.4s;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .dashboard {
      grid-template-columns: 1fr;
    }
    
    .miancards .card {
      min-height: 140px;
    }
  }
  /* Fix hover arrow on sidebar nav links */
  .sidebar .nav-link {
    position: relative; /* Add position relative to all nav links */
    font-size: 1.1rem;
    padding: 12px 10px;
    color: #fff;
    transition: background-color 0.2s ease;
  }

  .sidebar .nav-link:hover {
    background-color: #3e8e41;
    border-radius: 0.25rem;
  }

  /* Add the arrow on hover with !important to ensure it appears */
  .sidebar .nav-link:hover::after {
    content: "▼" !important;
    position: absolute !important;
    right: -12px !important;
    top: -6px !important;
    font-size: 20px !important;
    transform: translateY(-50%) !important;
    rotate: 208deg !important;
    color: white !important;
    background-color: transparent !important;
    display: block !important;
    z-index: 1000 !important;
  }

  /* Keep existing active state arrow */
  .nav-link.active {
    background-color: #3e8e41;
    position: relative;
  }

  .nav-link.active::after {
    content: "▼";
    position: absolute;
    right: -12px;
    top: -6px;
    font-size: 20px;
    transform: translateY(-50%);
    rotate: 208deg;
    color: white;
    background-color: transparent;
    z-index: 1000;
  }

  /* Ensure dropdown items don't show the arrow */
  .collapse .nav-link:hover::after {
    display: none !important;
  }
  /* Fix hover arrow specifically for Setup dropdown */
  .sidebar .nav-link[data-bs-toggle="collapse"] {
    position: relative;
  }

  .sidebar .nav-link[data-bs-toggle="collapse"]:hover {
    background-color: #3e8e41;
    border-radius: 0.25rem;
  }

  .sidebar .nav-link[data-bs-toggle="collapse"]:hover::after {
    content: "▼" !important;
    position: absolute !important;
    right: -12px !important;
    top: -6px !important;
    font-size: 20px !important;
    transform: translateY(-50%) !important;
    rotate: 208deg !important;
    color: white !important;
    background-color: transparent !important;
    display: block !important;
    z-index: 1000 !important;
  }

  /* Ensure only the top-level Setup link shows the arrow, not the dropdown items */
  .sidebar #setupDropdown .nav-link:hover::after {
    display: none !important;
  }
  /* Dashboard action boxes */
  .dashboard-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .action-box {
    flex: 1;
    min-width: 100px;
    border: 1px solid #20c997;
    border-radius: 5px;
    padding: 15px 10px;
    text-align: center;
    background-color: white;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .action-box:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
  }

  .action-box i {
    font-size: 24px;
    color: #20c997;
    margin-bottom: 8px;
  }

  .action-box span {
    display: block;
    font-size: 12px;
    color: #6c757d;
  }
  /* Add consistent button styling for tables */
  .actions .edit-btn,
  .actions .delete-btn,
  .btn-warning.btn-sm,
  .btn-danger.btn-sm,
  .btn-primary.btn-sm,
  button.delete-btn,
  .checkOutBtn {
    min-width: 70px;
    text-align: center;
    padding: 6px 12px;
    margin: 2px;
    display: inline-block;
  }

  /* Improve responsive layout for action buttons */
  @media (max-width: 768px) {
    .actions {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      gap: 10px;
    }
    
    .actions .edit-btn,
    .actions .delete-btn,
    .actions .btn-warning,
    .actions .btn-danger,
    .actions .btn-primary,
    .actions button.delete-btn,
    .actions .checkOutBtn {
      min-width: 70px;
      text-align: center;
    }
    
    /* Fix for forms inside actions */
    .actions form {
      flex: 0 0 auto;
      display: inline-block;
    }
    
    .table-row div[data-label="Actions"],
    .table-rowuser div[data-label="Actions"],
    .table-rowspro div[data-label="Actions"],
    .table-rowcity div[data-label="Actions"],
    .table-rowbed div[data-label="Actions"],
    .table-rowes div[data-label="Actions"],
    .table-rowentry div[data-label="Actions"] {
      display: flex !important;
      flex-direction: column !important;
    }
    
    .table-row div[data-label="Actions"]::before,
    .table-rowuser div[data-label="Actions"]::before,
    .table-rowspro div[data-label="Actions"]::before,
    .table-rowcity div[data-label="Actions"]::before,
    .table-rowbed div[data-label="Actions"]::before,
    .table-rowes div[data-label="Actions"]::before,
    .table-rowentry div[data-label="Actions"]::before {
      margin-bottom: 8px;
    }
    
    .table-row div[data-label="Actions"] .actions,
    .table-rowuser div[data-label="Actions"] .actions,
    .table-rowspro div[data-label="Actions"] .actions,
    .table-rowcity div[data-label="Actions"] .actions,
    .table-rowbed div[data-label="Actions"] .actions,
    .table-rowes div[data-label="Actions"] .actions,
    .table-rowentry div[data-label="Actions"] .actions {
      margin-top: 5px;
    }
  }
  /* Swiper carousel styling fixes */
  .swiper {
    position: relative;
    padding-bottom: 40px;
    margin-bottom: 20px;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
  }

  .swiper-wrapper {
    align-items: stretch;
  }

  .swiper-slide {
    height: auto;
    display: flex;
  }

  /* Room Card Styles - Reduced height */
  .room-card {
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: auto;
    width: 100%;
    margin-bottom: 0;
  }

  .room-card h5 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    text-align: left;
  }

  /* Bed icons - smaller size */
  .room-card .fas.fa-bed {
    font-size: 35px !important;
    margin-bottom: 5px;
  }

  /* Bed name - smaller text */
  .room-card .d-flex > div p {
    margin: 0;
    font-size: 0.8rem;
  }

  /* Bed icons container - tighter spacing */
  .room-card .d-flex {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 0;
  }

  /* Individual bed container - reduced margins */
  .room-card .d-flex > div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0;
  }

  /* Swiper container - reduced padding */
  .swiper {
    padding-bottom: 30px;
    margin-bottom: 15px;
  }

  /* Navigation positioning - closer to cards */
  .swiper-button-prev.position-static,
  .swiper-button-next.position-static {
    margin-top: -5px;
  }

  .swiper-button-prev.position-static,
  .swiper-button-next.position-static {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    margin: 0;
    width: 40px;
    height: 40px;
    background-color: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .swiper-button-prev.position-static:after,
  .swiper-button-next.position-static:after {
    font-size: 18px;
    color: white;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .swiper {
      padding-left: 10px;
      padding-right: 10px;
    }
    
    .swiper-button-prev.position-static,
    .swiper-button-next.position-static {
      width: 35px;
      height: 35px;
    }
    
    .swiper-button-prev.position-static:after,
    .swiper-button-next.position-static:after {
      font-size: 16px;
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
    
  


  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
  }
  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
   

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35);
    }
  }
}
  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35);
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }
  }
  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35);
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
   
  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
    
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
  }
  @media(max-width: 768px) {

    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height:30px;
    }
  
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

  @media (max-width: 768px) {
    .swiper-button-next,
    .swiper-button-prev {
      width: 30px;
      height: 30px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 16px;
    }
    
    .swiper-button-prev {
      left: calc(50% - 35px);
    }
    
    .swiper-button-next {
      right: calc(50% - 35px);
    }
  }
  /* Room card adjustments */
  .room-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Ensure consistent height for room cards */
  .swiper-slide .room-card {
    min-height: 200px;
  }

