<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Provinces - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="true" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse show">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3 active" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-map me-2"></i>Province Management</h1>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="row">
              <!-- Provinces Table -->
              <div class="col-md-8">
                <div class="table-section">
                  <h2>Provinces</h2>
                  <div class="table-container">
                    <div class="table-headerpro">
                      <div>SNO</div>
                      <div>Province Name</div>
                      <div>Actions</div>
                    </div>

                    <div id="provincesTableBody">
                      <!-- Provinces will be loaded dynamically -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- Add Province Form -->
              <div class="col-md-4">
                <div class="form-section">
                  <form class="card" id="provinceForm" onsubmit="addProvince(event)">
                    <div class="card-header">
                      <h3 class="card-title">Add Province</h3>
                    </div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label class="form-label required">Province Name</label>
                        <div>
                          <input type="text" name="province_name" class="form-control" placeholder="Province Name" required>
                        </div>
                      </div>
                    </div>
                    <div class="card-footer text-end">
                      <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the provinces page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadProvinces();
    });

    function loadProvinces() {
      const provinces = dataManager.getAll('provinces');
      const container = document.getElementById('provincesTableBody');

      if (container) {
        container.innerHTML = '';
        provinces.forEach((province, index) => {
          const row = document.createElement('div');
          row.className = 'table-rowspro';
          row.innerHTML = `
            <div data-label="SNO">${index + 1}</div>
            <div data-label="Province Name">${province.province_name}</div>
            <div data-label="Actions">
              <button class="delete-province-btn btn btn-danger" onclick="deleteProvince(${province.id})" aria-label="Delete Province">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          `;
          container.appendChild(row);
        });
      }
    }

    function addProvince(event) {
      event.preventDefault();
      const form = event.target;
      const formData = new FormData(form);

      const provinceName = formData.get('province_name');
      if (!provinceName) {
        alert('Please enter province name');
        return;
      }

      const provinceData = {
        province_name: provinceName
      };

      dataManager.add('provinces', provinceData);
      loadProvinces();
      form.reset();
      alert('Province added successfully!');
    }

    function deleteProvince(id) {
      if (confirm('Are you sure you want to delete this province?')) {
        dataManager.delete('provinces', id);
        loadProvinces();
        alert('Province deleted successfully!');
      }
    }
  </script>
</body>
</html>
