<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Settings - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
  <style>


    .profile-section {
      text-align: center;
      margin-bottom: 2rem;
      padding: 2rem;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 15px;
    }

    .profile-image-container {
      position: relative;
      display: inline-block;
      margin-bottom: 1rem;
    }

    .profile-image {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      border: 5px solid #4CAF50;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .image-upload-btn {
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      box-shadow: 0 3px 10px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
    }

    .image-upload-btn:hover {
      background: #45a049;
      transform: scale(1.1);
    }

    .form-section {
      margin-bottom: 2rem;
    }

    .form-section h4 {
      color: #4CAF50;
      font-weight: bold;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #e9ecef;
    }

    .form-control {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 0.75rem;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
    }

    .btn-primary {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      border: none;
      border-radius: 8px;
      padding: 0.75rem 2rem;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }

    .btn-danger {
      border-radius: 8px;
      padding: 0.75rem 2rem;
      font-weight: bold;
    }

    .admin-info {
      background: #e8f5e8;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
    }

    .camera-section {
      text-align: center;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      margin: 1rem 0;
      display: none;
    }

    #video {
      border: 2px solid #4CAF50;
      border-radius: 8px;
      margin: 1rem 0;
    }

    .capture-controls {
      margin: 1rem 0;
    }

    .capture-controls button {
      margin: 0 0.5rem;
    }
  </style>
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile" id="sidebarProfileImage">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-gear me-2"></i>Admin Settings</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <a href="index.html" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
              </a>
            </div>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="settings-container">
              <!-- Profile Section -->
              <div class="profile-section">
                <div class="profile-image-container">
                  <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="Admin Profile" class="profile-image" id="profileImage">
                  <button class="image-upload-btn" onclick="showImageOptions()" title="Change Profile Picture">
                    <i class="fas fa-camera"></i>
                  </button>
                </div>
                <h3 id="displayName">Admin User</h3>
                <p class="text-muted">System Administrator</p>
                <div class="admin-info">
                  <small><i class="bi bi-calendar me-2"></i>Account Created: <span id="accountCreated">January 1, 2024</span></small>
                </div>
              </div>

              <!-- Account Information Form -->
              <form id="settingsForm" onsubmit="updateSettings(event)">
                <div class="form-section">
                  <h4><i class="bi bi-person me-2"></i>Account Information</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="fullName" class="form-label">Full Name</label>
                      <input type="text" class="form-control" id="fullName" name="fullName" value="Admin User" required>
                    </div>
                    <div class="col-md-6">
                      <label for="email" class="form-label">Email Address</label>
                      <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="col-md-6">
                      <label for="phone" class="form-label">Phone Number</label>
                      <input type="tel" class="form-control" id="phone" name="phone" value="+92-300-1234567">
                    </div>
                    <div class="col-md-6">
                      <label for="role" class="form-label">Role</label>
                      <input type="text" class="form-control" id="role" name="role" value="System Administrator" readonly>
                    </div>
                  </div>
                </div>

                <div class="form-section">
                  <h4><i class="bi bi-shield-lock me-2"></i>Security Settings</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="currentPassword" class="form-label">Current Password</label>
                      <input type="password" class="form-control" id="currentPassword" name="currentPassword">
                    </div>
                    <div class="col-md-6">
                      <label for="newPassword" class="form-label">New Password</label>
                      <input type="password" class="form-control" id="newPassword" name="newPassword">
                    </div>
                    <div class="col-md-6">
                      <label for="confirmPassword" class="form-label">Confirm New Password</label>
                      <input type="password" class="form-control" id="confirmPassword" name="confirmPassword">
                    </div>
                  </div>
                </div>

                <div class="form-section">
                  <h4><i class="bi bi-sliders me-2"></i>System Preferences</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="language" class="form-label">Language</label>
                      <select class="form-control" id="language" name="language">
                        <option value="en">English</option>
                        <option value="ur">Urdu</option>
                      </select>
                    </div>
                    <div class="col-md-6">
                      <label for="timezone" class="form-label">Timezone</label>
                      <select class="form-control" id="timezone" name="timezone">
                        <option value="Asia/Karachi">Pakistan Standard Time</option>
                        <option value="UTC">UTC</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                  <button type="submit" class="btn btn-primary me-3">
                    <i class="fas fa-save me-2"></i>Save Changes
                  </button>
                  <button type="button" class="btn btn-danger" onclick="resetSettings()">
                    <i class="fas fa-undo me-2"></i>Reset to Default
                  </button>
                </div>
              </form>
            </div>

            <!-- Camera Section for Profile Picture -->
            <div class="camera-section" id="cameraSection">
              <h5><i class="fas fa-camera me-2"></i>Capture New Profile Picture</h5>
              <video id="video" width="300" height="200" autoplay></video>
              <canvas id="canvas" width="300" height="200" style="display:none;"></canvas>
              <div class="capture-controls">
                <button type="button" class="btn btn-primary" onclick="capturePhoto()">
                  <i class="fa fa-camera me-2"></i>Capture Photo
                </button>
                <button type="button" class="btn btn-secondary" onclick="hideCameraSection()">
                  <i class="fa fa-times me-2"></i>Cancel
                </button>
              </div>
              <div id="photoPreview" class="mt-2"></div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- File Upload Input (Hidden) -->
  <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event)">

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadSettings();
    });

    function loadSettings() {
      const settings = JSON.parse(localStorage.getItem('adminSettings') || '{}');
      const defaults = {
        fullName: 'Admin User',
        email: '<EMAIL>',
        phone: '+92-300-1234567',
        profileImage: '/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg'
      };

      const data = { ...defaults, ...settings };

      document.getElementById('fullName').value = data.fullName;
      document.getElementById('email').value = data.email;
      document.getElementById('phone').value = data.phone;
      document.getElementById('displayName').textContent = data.fullName;
      document.getElementById('currentUser').textContent = data.fullName;
      document.getElementById('sidebarUserName').textContent = data.fullName;

      if (data.profileImage) {
        document.getElementById('profileImage').src = data.profileImage;
        document.getElementById('sidebarProfileImage').src = data.profileImage;
      }
    }

    function updateSettings(event) {
      event.preventDefault();
      const formData = new FormData(event.target);

      const settings = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        lastUpdated: new Date().toLocaleString()
      };

      const current = JSON.parse(localStorage.getItem('adminSettings') || '{}');
      localStorage.setItem('adminSettings', JSON.stringify({...current, ...settings}));

      loadSettings();
      alert('Settings updated successfully!');
      document.getElementById('newPassword').value = '';
    }

    function handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imageData = e.target.result;

          const current = JSON.parse(localStorage.getItem('adminSettings') || '{}');
          current.profileImage = imageData;
          localStorage.setItem('adminSettings', JSON.stringify(current));

          document.getElementById('profileImage').src = imageData;
          document.getElementById('sidebarProfileImage').src = imageData;

          alert('Profile picture updated!');
        };
        reader.readAsDataURL(file);
      }
    }

    function resetSettings() {
      if (confirm('Reset all settings to default?')) {
        localStorage.removeItem('adminSettings');
        loadSettings();
        alert('Settings reset!');
      }
    }

    function toggleSidebar() {
      document.getElementById('sidebarMenu').classList.toggle('active');
    }

    function logout() {
      if (confirm('Are you sure you want to logout?')) {
        window.location.href = 'index.html';
      }
    }

    function updateDateTime() {
      const element = document.getElementById('currentDateTime');
      if (element) {
        element.textContent = new Date().toLocaleString();
      }
    }
  </script>
</body>
</html>
  <script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadSettings();
    });

    function loadSettings() {
      const settings = JSON.parse(localStorage.getItem('adminSettings') || '{}');
      const defaults = {
        fullName: 'Admin User',
        email: '<EMAIL>',
        phone: '+92-300-1234567',
        profileImage: '/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg'
      };

      const data = { ...defaults, ...settings };

      document.getElementById('fullName').value = data.fullName;
      document.getElementById('email').value = data.email;
      document.getElementById('phone').value = data.phone;
      document.getElementById('displayName').textContent = data.fullName;
      document.getElementById('currentUser').textContent = data.fullName;
      document.getElementById('sidebarUserName').textContent = data.fullName;

      if (data.profileImage) {
        document.getElementById('profileImage').src = data.profileImage;
        document.getElementById('sidebarProfileImage').src = data.profileImage;
      }
    }

    function updateSettings(event) {
      event.preventDefault();
      const formData = new FormData(event.target);

      const settings = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        lastUpdated: new Date().toLocaleString()
      };

      const current = JSON.parse(localStorage.getItem('adminSettings') || '{}');
      localStorage.setItem('adminSettings', JSON.stringify({...current, ...settings}));

      loadSettings();
      alert('Settings updated successfully!');
      document.getElementById('newPassword').value = '';
    }

    function handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imageData = e.target.result;

          const current = JSON.parse(localStorage.getItem('adminSettings') || '{}');
          current.profileImage = imageData;
          localStorage.setItem('adminSettings', JSON.stringify(current));

          document.getElementById('profileImage').src = imageData;
          document.getElementById('sidebarProfileImage').src = imageData;

          alert('Profile picture updated!');
        };
        reader.readAsDataURL(file);
      }
    }

    function resetSettings() {
      if (confirm('Reset all settings to default?')) {
        localStorage.removeItem('adminSettings');
        loadSettings();
        alert('Settings reset!');
      }
    }

    function toggleSidebar() {
      document.getElementById('sidebarMenu').classList.toggle('active');
    }

    function logout() {
      if (confirm('Are you sure you want to logout?')) {
        window.location.href = 'index.html';
      }
    }

    function updateDateTime() {
      const element = document.getElementById('currentDateTime');
      if (element) {
        element.textContent = new Date().toLocaleString();
      }
    }
  </script>
</body>
</html>
