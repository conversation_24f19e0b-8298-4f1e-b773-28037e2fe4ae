<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Settings - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
  <style>
    .settings-container {
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
      padding: 2rem;
      margin: 2rem auto;
      max-width: 800px;
    }

    .profile-section {
      text-align: center;
      margin-bottom: 2rem;
      padding: 2rem;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 15px;
    }

    .profile-image-container {
      position: relative;
      display: inline-block;
      margin-bottom: 1rem;
    }

    .profile-image {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      border: 5px solid #4CAF50;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .image-upload-btn {
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      box-shadow: 0 3px 10px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
    }

    .image-upload-btn:hover {
      background: #45a049;
      transform: scale(1.1);
    }

    .form-section {
      margin-bottom: 2rem;
    }

    .form-section h4 {
      color: #4CAF50;
      font-weight: bold;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #e9ecef;
    }

    .form-control {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 0.75rem;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
    }

    .btn-primary {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      border: none;
      border-radius: 8px;
      padding: 0.75rem 2rem;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }

    .btn-danger {
      border-radius: 8px;
      padding: 0.75rem 2rem;
      font-weight: bold;
    }

    .admin-info {
      background: #e8f5e8;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
    }

    .camera-section {
      text-align: center;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      margin: 1rem 0;
      display: none;
    }

    #video {
      border: 2px solid #4CAF50;
      border-radius: 8px;
      margin: 1rem 0;
    }

    .capture-controls {
      margin: 1rem 0;
    }

    .capture-controls button {
      margin: 0 0.5rem;
    }
  </style>
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile" id="sidebarProfileImage">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-gear me-2"></i>Admin Settings</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <a href="index.html" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
              </a>
            </div>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="settings-container">
              <!-- Profile Section -->
              <div class="profile-section">
                <div class="profile-image-container">
                  <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="Admin Profile" class="profile-image" id="profileImage">
                  <button class="image-upload-btn" onclick="showImageOptions()" title="Change Profile Picture">
                    <i class="fas fa-camera"></i>
                  </button>
                </div>
                <h3 id="displayName">Admin User</h3>
                <p class="text-muted">System Administrator</p>
                <div class="admin-info">
                  <small><i class="bi bi-calendar me-2"></i>Account Created: <span id="accountCreated">January 1, 2024</span></small>
                </div>
              </div>

              <!-- Account Information Form -->
              <form id="settingsForm" onsubmit="updateSettings(event)">
                <div class="form-section">
                  <h4><i class="bi bi-person me-2"></i>Account Information</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="fullName" class="form-label">Full Name</label>
                      <input type="text" class="form-control" id="fullName" name="fullName" value="Admin User" required>
                    </div>
                    <div class="col-md-6">
                      <label for="email" class="form-label">Email Address</label>
                      <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="col-md-6">
                      <label for="phone" class="form-label">Phone Number</label>
                      <input type="tel" class="form-control" id="phone" name="phone" value="+92-300-1234567">
                    </div>
                    <div class="col-md-6">
                      <label for="role" class="form-label">Role</label>
                      <input type="text" class="form-control" id="role" name="role" value="System Administrator" readonly>
                    </div>
                  </div>
                </div>

                <div class="form-section">
                  <h4><i class="bi bi-shield-lock me-2"></i>Security Settings</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="currentPassword" class="form-label">Current Password</label>
                      <input type="password" class="form-control" id="currentPassword" name="currentPassword">
                    </div>
                    <div class="col-md-6">
                      <label for="newPassword" class="form-label">New Password</label>
                      <input type="password" class="form-control" id="newPassword" name="newPassword">
                    </div>
                    <div class="col-md-6">
                      <label for="confirmPassword" class="form-label">Confirm New Password</label>
                      <input type="password" class="form-control" id="confirmPassword" name="confirmPassword">
                    </div>
                  </div>
                </div>

                <div class="form-section">
                  <h4><i class="bi bi-sliders me-2"></i>System Preferences</h4>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label for="language" class="form-label">Language</label>
                      <select class="form-control" id="language" name="language">
                        <option value="en">English</option>
                        <option value="ur">Urdu</option>
                      </select>
                    </div>
                    <div class="col-md-6">
                      <label for="timezone" class="form-label">Timezone</label>
                      <select class="form-control" id="timezone" name="timezone">
                        <option value="Asia/Karachi">Pakistan Standard Time</option>
                        <option value="UTC">UTC</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                  <button type="submit" class="btn btn-primary me-3">
                    <i class="fas fa-save me-2"></i>Save Changes
                  </button>
                  <button type="button" class="btn btn-danger" onclick="resetSettings()">
                    <i class="fas fa-undo me-2"></i>Reset to Default
                  </button>
                </div>
              </form>
            </div>

            <!-- Camera Section for Profile Picture -->
            <div class="camera-section" id="cameraSection">
              <h5><i class="fas fa-camera me-2"></i>Capture New Profile Picture</h5>
              <video id="video" width="300" height="200" autoplay></video>
              <canvas id="canvas" width="300" height="200" style="display:none;"></canvas>
              <div class="capture-controls">
                <button type="button" class="btn btn-primary" onclick="capturePhoto()">
                  <i class="fa fa-camera me-2"></i>Capture Photo
                </button>
                <button type="button" class="btn btn-secondary" onclick="hideCameraSection()">
                  <i class="fa fa-times me-2"></i>Cancel
                </button>
              </div>
              <div id="photoPreview" class="mt-2"></div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- File Upload Input (Hidden) -->
  <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event)">

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    let videoStream = null;

    // Initialize the settings page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadAdminSettings();
    });

    function loadAdminSettings() {
      // Load admin settings from localStorage
      const adminSettings = JSON.parse(localStorage.getItem('adminSettings') || '{}');

      // Set default values if no settings exist
      const defaultSettings = {
        fullName: 'Admin User',
        email: '<EMAIL>',
        phone: '+92-300-1234567',
        role: 'System Administrator',
        language: 'en',
        timezone: 'Asia/Karachi',
        profileImage: '/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg',
        accountCreated: 'January 1, 2024'
      };

      const settings = { ...defaultSettings, ...adminSettings };

      // Update form fields
      document.getElementById('fullName').value = settings.fullName;
      document.getElementById('email').value = settings.email;
      document.getElementById('phone').value = settings.phone;
      document.getElementById('role').value = settings.role;
      document.getElementById('language').value = settings.language;
      document.getElementById('timezone').value = settings.timezone;

      // Update profile display
      document.getElementById('displayName').textContent = settings.fullName;
      document.getElementById('accountCreated').textContent = settings.accountCreated;
      document.getElementById('currentUser').textContent = settings.fullName;
      document.getElementById('sidebarUserName').textContent = settings.fullName;

      // Update profile images
      if (settings.profileImage) {
        document.getElementById('profileImage').src = settings.profileImage;
        document.getElementById('sidebarProfileImage').src = settings.profileImage;
      }
    }

    function updateSettings(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const newPassword = formData.get('newPassword');
      const confirmPassword = formData.get('confirmPassword');

      // Validate password if changing
      if (newPassword && newPassword !== confirmPassword) {
        alert('New passwords do not match!');
        return;
      }

      // Get current settings
      const currentSettings = JSON.parse(localStorage.getItem('adminSettings') || '{}');

      // Update settings
      const updatedSettings = {
        ...currentSettings,
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        language: formData.get('language'),
        timezone: formData.get('timezone'),
        lastUpdated: new Date().toLocaleString()
      };

      // Save to localStorage
      localStorage.setItem('adminSettings', JSON.stringify(updatedSettings));

      // Update display
      loadAdminSettings();

      alert('Settings updated successfully!');

      // Clear password fields
      document.getElementById('currentPassword').value = '';
      document.getElementById('newPassword').value = '';
      document.getElementById('confirmPassword').value = '';
    }

    function resetSettings() {
      if (confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
        localStorage.removeItem('adminSettings');
        loadAdminSettings();
        alert('Settings reset to default values!');
      }
    }

    function showImageOptions() {
      const choice = confirm('Choose image source:\nOK = Take Photo with Camera\nCancel = Upload from File');

      if (choice) {
        showCameraSection();
      } else {
        document.getElementById('fileInput').click();
      }
    }

    function showCameraSection() {
      const cameraSection = document.getElementById('cameraSection');
      cameraSection.style.display = 'block';

      // Start camera
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          videoStream = stream;
          document.getElementById('video').srcObject = stream;
        })
        .catch(err => {
          console.error('Error accessing camera: ', err);
          alert('Camera not available. Please use file upload instead.');
          hideCameraSection();
        });
    }

    function hideCameraSection() {
      const cameraSection = document.getElementById('cameraSection');
      cameraSection.style.display = 'none';

      // Stop camera stream
      if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
      }
    }

    function capturePhoto() {
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const context = canvas.getContext('2d');

      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64 image
      const imageData = canvas.toDataURL('image/png');

      // Show preview
      const preview = document.getElementById('photoPreview');
      preview.innerHTML = `
        <div class="mt-3">
          <img src="${imageData}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 3px solid #4CAF50;">
          <div class="mt-2">
            <button class="btn btn-success btn-sm" onclick="saveProfileImage('${imageData}')">
              <i class="fa fa-check me-2"></i>Use This Photo
            </button>
            <button class="btn btn-secondary btn-sm" onclick="capturePhoto()">
              <i class="fa fa-redo me-2"></i>Retake
            </button>
          </div>
        </div>
      `;
    }

    function handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imageData = e.target.result;

          // Show confirmation
          if (confirm('Use this image as your profile picture?')) {
            saveProfileImage(imageData);
          }
        };
        reader.readAsDataURL(file);
      }
    }

    function saveProfileImage(imageData) {
      // Get current settings
      const currentSettings = JSON.parse(localStorage.getItem('adminSettings') || '{}');

      // Update profile image
      currentSettings.profileImage = imageData;
      currentSettings.lastUpdated = new Date().toLocaleString();

      // Save to localStorage
      localStorage.setItem('adminSettings', JSON.stringify(currentSettings));

      // Update all profile images on page
      document.getElementById('profileImage').src = imageData;
      document.getElementById('sidebarProfileImage').src = imageData;

      // Hide camera section
      hideCameraSection();

      alert('Profile picture updated successfully!');
    }

    // Sidebar toggle
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebarMenu');
      sidebar.classList.toggle('active');
    }

    // Logout function
    function logout() {
      if (confirm('Are you sure you want to logout?')) {
        alert('Logged out successfully!');
        window.location.href = 'index.html';
      }
    }

    function updateDateTime() {
      const dateTimeElement = document.getElementById('currentDateTime');
      if (dateTimeElement) {
        dateTimeElement.textContent = formatDateTime();
      }
    }

    function formatDateTime(date = new Date()) {
      return date.toLocaleString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      });
    }
  </script>
</body>
</html>
