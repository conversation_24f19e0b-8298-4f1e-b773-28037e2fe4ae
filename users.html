<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Users - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="true" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse show">
                <a class="nav-link ms-3 active" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-people me-2"></i>User Management</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="bi bi-plus-circle me-2"></i>Add New User
              </button>
            </div>
          </div>

          <!-- Users Table -->
          <div class="container">
            <div class="table-container">
              <div class="table-headeruser">
                <div>Name</div>
                <div>Email</div>
                <div>Role</div>
                <div>Actions</div>
              </div>

              <div id="usersTableBody">
                <!-- Users will be loaded dynamically -->
              </div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Add User Modal -->
  <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form id="addUserForm" onsubmit="addUser(event)">
          <div class="modal-body">
            <div class="mb-3">
              <label for="userName" class="form-label">Full Name</label>
              <input type="text" name="name" id="userName" class="form-control" placeholder="Full Name" required>
            </div>
            <div class="mb-3">
              <label for="userEmail" class="form-label">Email</label>
              <input type="email" name="email" id="userEmail" class="form-control" placeholder="Email" required>
            </div>
            <div class="mb-3">
              <label for="userPassword" class="form-label">Password</label>
              <input type="password" name="password" id="userPassword" class="form-control" placeholder="Password" required>
            </div>
            <div class="mb-3">
              <label for="userRole" class="form-label">Role</label>
              <select name="role_id" id="userRole" class="form-control" required>
                <option value="">Select Role</option>
                <option value="1">Administrator</option>
                <option value="2">Manager</option>
                <option value="3">Staff</option>
              </select>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary">Add User</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the users page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadUsers();

      // Setup form event listener
      const addUserForm = document.getElementById('addUserForm');
      if (addUserForm) {
        addUserForm.addEventListener('submit', function(event) {
          event.preventDefault();
          const formData = new FormData(event.target);

          const userData = {
            name: formData.get('name'),
            email: formData.get('email'),
            role_name: formData.get('role_id') === '1' ? 'Administrator' :
                       formData.get('role_id') === '2' ? 'Manager' : 'Staff'
          };

          dataManager.add('users', userData);
          loadUsers();
          event.target.reset();

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
          modal.hide();

          alert('User added successfully!');
        });
      }
    });

    function loadUsers() {
      const users = dataManager.getAll('users');
      const container = document.getElementById('usersTableBody');

      if (container) {
        container.innerHTML = '';
        users.forEach(user => {
          const row = document.createElement('div');
          row.className = 'table-rowuser';
          row.innerHTML = `
            <div class="name-cell" data-label="Name">${user.name}</div>
            <div data-label="Email">${user.email}</div>
            <div data-label="Role">${user.role_name}</div>
            <div class="actions" data-label="Actions">
              <button class="edit-btn" onclick="editUser(${user.id})" aria-label="Edit user">Edit</button>
              <button class="delete-btn" onclick="deleteUser(${user.id})" aria-label="Delete user">Delete</button>
            </div>
          `;
          container.appendChild(row);
        });
      }
    }

    function editUser(id) {
      const user = dataManager.getById('users', id);
      if (user) {
        const newName = prompt('Enter new name:', user.name);
        const newEmail = prompt('Enter new email:', user.email);

        if (newName && newEmail) {
          dataManager.update('users', id, { name: newName, email: newEmail });
          loadUsers();
          alert('User updated successfully!');
        }
      }
    }

    function deleteUser(id) {
      if (confirm('Are you sure you want to delete this user?')) {
        dataManager.delete('users', id);
        loadUsers();
        alert('User deleted successfully!');
      }
    }
  </script>
</body>
</html>
