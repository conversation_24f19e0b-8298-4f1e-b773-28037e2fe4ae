<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Messages - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="#" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>
      
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-chat me-2"></i>Message Center</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button class="btn btn-success" onclick="sendMessage()">
                <i class="bi bi-send me-2"></i>Send Message
              </button>
            </div>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="row">
              <!-- Contact List -->
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-people me-2"></i>Contact List</h5>
                    <div class="form-check">
                      <input type="checkbox" id="selectAll" class="form-check-input">
                      <label for="selectAll" class="form-check-label">Select All</label>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hover">
                        <thead>
                          <tr>
                            <th>Name</th>
                            <th>Mobile Number</th>
                            <th>Select</th>
                          </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                          <!-- Messages will be loaded dynamically -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Message Compose -->
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-pencil me-2"></i>Compose Message</h5>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label for="message" class="form-label">Message</label>
                      <textarea class="form-control" id="message" rows="8" placeholder="Type your message here..."></textarea>
                    </div>
                    <div class="mb-3">
                      <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Select contacts from the list and compose your message.
                      </small>
                    </div>
                    <div class="d-grid">
                      <button class="btn btn-primary" onclick="sendMessage()">
                        <i class="bi bi-send me-2"></i>Send Message
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Message Templates -->
                <div class="card mt-3">
                  <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-file-text me-2"></i>Quick Templates</h6>
                  </div>
                  <div class="card-body">
                    <div class="d-grid gap-2">
                      <button class="btn btn-outline-secondary btn-sm" onclick="useTemplate('welcome')">
                        Welcome Message
                      </button>
                      <button class="btn btn-outline-secondary btn-sm" onclick="useTemplate('checkout')">
                        Check-out Reminder
                      </button>
                      <button class="btn btn-outline-secondary btn-sm" onclick="useTemplate('thankyou')">
                        Thank You Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Message History -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Message History</h5>
                  </div>
                  <div class="card-body">
                    <div id="messageHistory">
                      <p class="text-muted">No messages sent yet.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the messages page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadContacts();
      loadMessageHistory();
      
      // Setup select all checkbox
      const selectAllCheckbox = document.getElementById('selectAll');
      if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
          const checkboxes = document.querySelectorAll('.unique-rowCheckbox');
          checkboxes.forEach(cb => cb.checked = this.checked);
        });
      }
    });

    function loadContacts() {
      const entries = dataManager.getAll('entries');
      const tableBody = document.getElementById('messagesTableBody');
      
      if (tableBody) {
        tableBody.innerHTML = '';
        
        // Remove duplicates based on mobile number
        const uniqueContacts = entries.filter((entry, index, self) => 
          index === self.findIndex(e => e.mobile_no === entry.mobile_no)
        );
        
        uniqueContacts.forEach(entry => {
          const row = document.createElement('tr');
          row.className = 'unique-tr';
          row.innerHTML = `
            <td class="unique-td">${entry.name}</td>
            <td class="unique-td">${entry.mobile_no}</td>
            <td class="unique-td">
              <input type="checkbox" class="unique-rowCheckbox form-check-input" data-name="${entry.name}" data-mobile="${entry.mobile_no}">
            </td>
          `;
          tableBody.appendChild(row);
        });
      }
    }

    function sendMessage() {
      const message = document.getElementById('message').value.trim();
      const selectedCheckboxes = document.querySelectorAll('.unique-rowCheckbox:checked');
      
      if (!message) {
        alert('Please enter a message');
        return;
      }
      
      if (selectedCheckboxes.length === 0) {
        alert('Please select at least one contact');
        return;
      }
      
      // Collect selected contacts
      const selectedContacts = Array.from(selectedCheckboxes).map(cb => ({
        name: cb.getAttribute('data-name'),
        mobile: cb.getAttribute('data-mobile')
      }));
      
      // Save message to history
      saveMessageToHistory(message, selectedContacts);
      
      alert(`Message sent to ${selectedCheckboxes.length} contacts!`);
      
      // Clear form
      document.getElementById('message').value = '';
      document.querySelectorAll('.unique-rowCheckbox').forEach(cb => cb.checked = false);
      document.getElementById('selectAll').checked = false;
      
      // Reload message history
      loadMessageHistory();
    }

    function useTemplate(templateType) {
      const messageTextarea = document.getElementById('message');
      let template = '';
      
      switch(templateType) {
        case 'welcome':
          template = 'Welcome to our Darga! We hope you have a comfortable stay. If you need any assistance, please don\'t hesitate to contact our staff.';
          break;
        case 'checkout':
          template = 'Dear guest, this is a friendly reminder about your check-out time. Please ensure you complete the check-out process before leaving. Thank you for staying with us.';
          break;
        case 'thankyou':
          template = 'Thank you for choosing our Darga for your stay. We hope you had a pleasant experience. We look forward to serving you again in the future.';
          break;
      }
      
      messageTextarea.value = template;
    }

    function saveMessageToHistory(message, contacts) {
      const messageHistory = JSON.parse(localStorage.getItem('messageHistory') || '[]');
      const newMessage = {
        id: Date.now(),
        message: message,
        contacts: contacts,
        timestamp: new Date().toLocaleString(),
        contactCount: contacts.length
      };
      
      messageHistory.unshift(newMessage); // Add to beginning
      
      // Keep only last 50 messages
      if (messageHistory.length > 50) {
        messageHistory.splice(50);
      }
      
      localStorage.setItem('messageHistory', JSON.stringify(messageHistory));
    }

    function loadMessageHistory() {
      const messageHistory = JSON.parse(localStorage.getItem('messageHistory') || '[]');
      const historyContainer = document.getElementById('messageHistory');
      
      if (messageHistory.length === 0) {
        historyContainer.innerHTML = '<p class="text-muted">No messages sent yet.</p>';
        return;
      }
      
      historyContainer.innerHTML = '';
      messageHistory.slice(0, 10).forEach(msg => { // Show last 10 messages
        const messageDiv = document.createElement('div');
        messageDiv.className = 'border-bottom pb-2 mb-2';
        messageDiv.innerHTML = `
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <p class="mb-1"><strong>Message:</strong> ${msg.message}</p>
              <small class="text-muted">
                <i class="bi bi-clock me-1"></i>${msg.timestamp} | 
                <i class="bi bi-people me-1"></i>${msg.contactCount} recipients
              </small>
            </div>
          </div>
        `;
        historyContainer.appendChild(messageDiv);
      });
    }
  </script>
</body>
</html>
