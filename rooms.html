<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rooms - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-door-open me-2"></i>Room Management</h1>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="row">
              <!-- Rooms Table -->
              <div class="col-md-8">
                <div class="table-section">
                  <h2>Rooms</h2>
                  <div class="table-container">
                    <div class="table-headers">
                      <div>SNO</div>
                      <div>Room</div>
                      <div>Beds</div>
                      <div>Status</div>
                      <div>Actions</div>
                    </div>

                    <div id="roomsTableBody">
                      <!-- Rooms will be loaded dynamically -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- Add Room Form -->
              <div class="col-md-4">
                <div class="form-section">
                  <form id="roomForm" class="card" onsubmit="addRoom(event)">
                    <div class="card-header">
                      <h3 class="card-title">Add Room</h3>
                    </div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label class="form-label required">Room Name</label>
                        <input type="text" name="room_name" class="form-control" placeholder="Room Name" required>
                      </div>
                      <div class="mb-3">
                        <label class="form-label required">Number of Beds</label>
                        <input type="number" name="room_beds" class="form-control" placeholder="Number of Beds" min="1" max="20" required>
                      </div>
                    </div>
                    <div class="card-footer text-end">
                      <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the rooms page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadRooms();
    });

    function loadRooms() {
      const rooms = dataManager.getAll('rooms');
      const container = document.getElementById('roomsTableBody');

      if (container) {
        container.innerHTML = '';
        rooms.forEach((room, index) => {
          const row = document.createElement('div');
          row.className = 'table-rowes';
          row.innerHTML = `
            <div data-label="SNO">${index + 1}</div>
            <div data-label="Room">${room.room_name}</div>
            <div data-label="Beds">${room.room_beds}</div>
            <div data-label="Status">
              <span class="badge ${room.status === 'available' ? 'bg-success' : 'bg-warning'}">
                ${room.status}
              </span>
            </div>
            <div class="actions" data-label="Actions">
              <button class="update-room-btn btn btn-primary btn-sm" onclick="editRoom(${room.id})" aria-label="update room">
                <i class="fa fa-pencil"></i>
              </button>
              <button class="delete-room-btn btn btn-danger btn-sm" onclick="deleteRoom(${room.id})" aria-label="Delete room">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          `;
          container.appendChild(row);
        });
      }
    }

    function addRoom(event) {
      event.preventDefault();
      const form = event.target;
      const formData = new FormData(form);

      const roomName = formData.get('room_name');
      const roomBeds = formData.get('room_beds');

      if (!roomName || !roomBeds) {
        alert('Please fill all fields');
        return;
      }

      const roomData = {
        room_name: roomName,
        room_beds: parseInt(roomBeds),
        status: 'available'
      };

      const newRoom = dataManager.add('rooms', roomData);

      // Create beds for the room
      for (let i = 1; i <= parseInt(roomBeds); i++) {
        const bedData = {
          bed_name: `Bed ${i}`,
          room_name: roomName,
          room_id: newRoom.id,
          status: 'available'
        };
        dataManager.add('beds', bedData);
      }

      loadRooms();
      form.reset();
      alert('Room added successfully!');
    }

    function editRoom(id) {
      const room = dataManager.getById('rooms', id);
      if (room) {
        const newName = prompt('Enter new room name:', room.room_name);
        if (newName) {
          dataManager.update('rooms', id, { room_name: newName });

          // Update bed room names
          const beds = dataManager.getAll('beds');
          beds.filter(bed => bed.room_id === id).forEach(bed => {
            dataManager.update('beds', bed.id, { room_name: newName });
          });

          loadRooms();
          alert('Room updated successfully!');
        }
      }
    }

    function deleteRoom(id) {
      if (confirm('Are you sure you want to delete this room? This will also delete all associated beds.')) {
        dataManager.delete('rooms', id);

        // Also delete associated beds
        const beds = dataManager.getAll('beds');
        beds.filter(bed => bed.room_id === id).forEach(bed => {
          dataManager.delete('beds', bed.id);
        });

        loadRooms();
        alert('Room deleted successfully!');
      }
    }
  </script>
</body>
</html>
