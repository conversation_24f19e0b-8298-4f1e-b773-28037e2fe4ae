// Darga Management System - JavaScript Implementation
// Replaces PHP/Laravel functionality with client-side JavaScript

// Data Storage using localStorage
class DataManager {
    constructor() {
        this.initializeData();
    }

    initializeData() {
        // Initialize default data if not exists
        if (!localStorage.getItem('users')) {
            localStorage.setItem('users', JSON.stringify([
                { id: 1, name: 'Admin User', email: '<EMAIL>', role_name: 'Administrator' },
                { id: 2, name: '<PERSON>', email: '<EMAIL>', role_name: 'Manager' }
            ]));
        }

        if (!localStorage.getItem('provinces')) {
            localStorage.setItem('provinces', JSON.stringify([
                { id: 1, province_name: 'Punjab' },
                { id: 2, province_name: 'Sindh' },
                { id: 3, province_name: 'KPK' },
                { id: 4, province_name: 'Balochistan' }
            ]));
        }

        if (!localStorage.getItem('cities')) {
            localStorage.setItem('cities', JSON.stringify([
                { id: 1, city_name: 'Lahore', province_name: 'Punjab', province_id: 1 },
                { id: 2, city_name: 'Karachi', province_name: 'Sindh', province_id: 2 },
                { id: 3, city_name: 'Peshawar', province_name: 'KPK', province_id: 3 },
                { id: 4, city_name: 'Quetta', province_name: 'Balochistan', province_id: 4 }
            ]));
        }

        if (!localStorage.getItem('rooms')) {
            localStorage.setItem('rooms', JSON.stringify([
                { id: 1, room_name: 'Room 1', room_beds: 7, status: 'available' },
                { id: 2, room_name: 'Room 2', room_beds: 7, status: 'available' },
                { id: 3, room_name: 'Room 3', room_beds: 7, status: 'available' }
            ]));
        }

        if (!localStorage.getItem('beds')) {
            localStorage.setItem('beds', JSON.stringify([
                { id: 1, bed_name: 'Bed 1', room_name: 'Room 1', room_id: 1, status: 'available' },
                { id: 2, bed_name: 'Bed 2', room_name: 'Room 1', room_id: 1, status: 'occupied' },
                { id: 3, bed_name: 'Bed 3', room_name: 'Room 1', room_id: 1, status: 'available' },
                { id: 4, bed_name: 'Bed 4', room_name: 'Room 1', room_id: 1, status: 'available' },
                { id: 5, bed_name: 'Bed 5', room_name: 'Room 1', room_id: 1, status: 'occupied' },
                { id: 6, bed_name: 'Bed 6', room_name: 'Room 1', room_id: 1, status: 'available' },
                { id: 7, bed_name: 'Bed 7', room_name: 'Room 1', room_id: 1, status: 'available' }
            ]));
        }

        if (!localStorage.getItem('entries')) {
            localStorage.setItem('entries', JSON.stringify([
                {
                    id: 1,
                    name: 'Ahmed Ali',
                    father_name: 'Muhammad Ali',
                    cnic_no: '12345-1234567-1',
                    mobile_no: '0300-1234567',
                    cast: 'Punjabi',
                    profession: 'Teacher',
                    city_name: 'Lahore',
                    province_name: 'Punjab',
                    room_name: 'Room 1',
                    bed_name: 'Bed 2',
                    check_in: '2025-01-01 10:00',
                    check_out: '',
                    status: 'checked_in',
                    created_at: '2025-01-01 10:00:00',
                    family: false,
                    group_members: '',
                    room_id: 1,
                    bed_ids: '2'
                },
                {
                    id: 2,
                    name: 'Fatima Khan',
                    father_name: 'Abdul Khan',
                    cnic_no: '54321-7654321-2',
                    mobile_no: '0301-7654321',
                    cast: 'Sindhi',
                    profession: 'Doctor',
                    city_name: 'Karachi',
                    province_name: 'Sindh',
                    room_name: 'Room 1',
                    bed_name: 'Bed 5',
                    check_in: '2025-01-02 14:30',
                    check_out: '',
                    status: 'checked_in',
                    created_at: '2025-01-02 14:30:00',
                    family: false,
                    group_members: '',
                    room_id: 1,
                    bed_ids: '5'
                }
            ]));
        }

        if (!localStorage.getItem('roles')) {
            localStorage.setItem('roles', JSON.stringify([
                { id: 1, name: 'Administrator' },
                { id: 2, name: 'Manager' },
                { id: 3, name: 'Staff' }
            ]));
        }
    }

    // Generic CRUD operations
    getAll(entity) {
        return JSON.parse(localStorage.getItem(entity) || '[]');
    }

    getById(entity, id) {
        const items = this.getAll(entity);
        return items.find(item => item.id == id);
    }

    add(entity, data) {
        const items = this.getAll(entity);
        const newId = items.length > 0 ? Math.max(...items.map(item => item.id)) + 1 : 1;
        const newItem = { ...data, id: newId };
        items.push(newItem);
        localStorage.setItem(entity, JSON.stringify(items));
        return newItem;
    }

    update(entity, id, data) {
        const items = this.getAll(entity);
        const index = items.findIndex(item => item.id == id);
        if (index !== -1) {
            items[index] = { ...items[index], ...data };
            localStorage.setItem(entity, JSON.stringify(items));
            return items[index];
        }
        return null;
    }

    delete(entity, id) {
        const items = this.getAll(entity);
        const filteredItems = items.filter(item => item.id != id);
        localStorage.setItem(entity, JSON.stringify(filteredItems));
        return true;
    }
}

// Initialize data manager
const dataManager = new DataManager();

// Utility functions
function formatDateTime(date = new Date()) {
    return date.toLocaleString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
}

function generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
}

// Page navigation
function showPage(pageId) {
    // Remove active class from all nav links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to the clicked nav link
    const clickedLink = document.querySelector(`.nav-link[onclick*="showPage('${pageId}')"]`);
    if (clickedLink) {
        clickedLink.classList.add('active');
    }

    // Hide all page sections
    document.querySelectorAll('.page-section').forEach(page => {
        page.classList.remove('active');
    });

    // Show the selected page
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');

        // Load data for specific pages
        switch(pageId) {
            case 'dashboard':
                loadDashboard();
                break;
            case 'user':
                loadUsers();
                break;
            case 'province':
                loadProvinces();
                break;
            case 'city':
                loadCities();
                break;
            case 'addEntry':
                loadEntries();
                break;
            case 'addRoom':
                loadRooms();
                break;
            case 'reports':
                loadReports();
                break;
            case 'messages':
                loadMessages();
                break;
        }
    }
}

// Sidebar toggle
function toggleSidebar() {
    const sidebar = document.getElementById('sidebarMenu');
    sidebar.classList.toggle('active');
}

// Logout function
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        alert('Logged out successfully!');
        // In a real application, you would redirect to login page
        location.reload();
    }
}

// Dashboard functions
function loadDashboard() {
    updateDateTime();
    updateDashboardStats();
    loadRecentEntries();
    loadRoomsCarousel();
    initializeCharts();
}

function updateDateTime() {
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = formatDateTime();
    }
}

function updateDashboardStats() {
    const entries = dataManager.getAll('entries');
    const rooms = dataManager.getAll('rooms');
    const beds = dataManager.getAll('beds');

    const today = new Date().toDateString();
    const todayEntries = entries.filter(entry =>
        new Date(entry.created_at).toDateString() === today
    );

    const individualEntries = entries.filter(entry => !entry.family);
    const familyEntries = entries.filter(entry => entry.family);
    const individualToday = todayEntries.filter(entry => !entry.family);
    const familyToday = todayEntries.filter(entry => entry.family);

    const availableRooms = rooms.filter(room => room.status === 'available');
    const availableBeds = beds.filter(bed => bed.status === 'available');

    // Update dashboard cards
    document.getElementById('totalIndividual').textContent = individualEntries.length;
    document.getElementById('individualToday').textContent = individualToday.length;
    document.getElementById('totalFamily').textContent = familyEntries.length;
    document.getElementById('familyToday').textContent = familyToday.length;
    document.getElementById('totalRooms').textContent = rooms.length;
    document.getElementById('availableRooms').textContent = availableRooms.length;
    document.getElementById('totalBeds').textContent = beds.length;
    document.getElementById('availableBeds').textContent = availableBeds.length;
}

function loadRecentEntries() {
    const entries = dataManager.getAll('entries');
    const recentEntries = entries.slice(-5); // Get last 5 entries
    const tableBody = document.getElementById('recentEntriesTable');

    if (tableBody) {
        tableBody.innerHTML = '';
        recentEntries.forEach(entry => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td data-label="Full Name">${entry.name}</td>
                <td data-label="F. Name">${entry.father_name}</td>
                <td data-label="CNIC">${entry.cnic_no}</td>
                <td data-label="Mobile">${entry.mobile_no}</td>
                <td data-label="Cast">${entry.cast}</td>
                <td data-label="Profession">${entry.profession}</td>
            `;
            tableBody.appendChild(row);
        });
    }
}

function loadRoomsCarousel() {
    const rooms = dataManager.getAll('rooms');
    const beds = dataManager.getAll('beds');
    const container = document.getElementById('roomsContainer');

    if (container) {
        container.innerHTML = '';
        rooms.forEach(room => {
            const roomBeds = beds.filter(bed => bed.room_id === room.id);
            const slide = document.createElement('div');
            slide.className = 'swiper-slide';

            let bedsHtml = '';
            roomBeds.forEach(bed => {
                const color = bed.status === 'available' ? 'color:#28a745;' : '';
                bedsHtml += `
                    <div>
                        <i class="fas fa-bed" style="font-size: 50px; cursor:pointer; ${color}"></i>
                        <p>${bed.bed_name}</p>
                    </div>
                `;
            });

            slide.innerHTML = `
                <div class="room-card border rounded-4 p-4 text-center shadow">
                    <h5 class="fw-bold text-start">${room.room_name}</h5>
                    <div class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                        ${bedsHtml}
                    </div>
                </div>
            `;
            container.appendChild(slide);
        });

        // Initialize Swiper
        if (window.Swiper) {
            new Swiper('.mySwiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    640: { slidesPerView: 2 },
                    768: { slidesPerView: 3 },
                    1024: { slidesPerView: 3 }
                }
            });
        }
    }
}

function initializeCharts() {
    // Pie Chart
    const pieCtx = document.getElementById('pieChart');
    if (pieCtx && window.Chart) {
        const entries = dataManager.getAll('entries');
        const cities = dataManager.getAll('cities');

        const cityData = {};
        entries.forEach(entry => {
            cityData[entry.city_name] = (cityData[entry.city_name] || 0) + 1;
        });

        new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(cityData),
                datasets: [{
                    data: Object.values(cityData),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Histogram Chart
    const histCtx = document.getElementById('histogram');
    if (histCtx && window.Chart) {
        const entries = dataManager.getAll('entries');
        const monthData = {};

        entries.forEach(entry => {
            const month = new Date(entry.created_at).toLocaleString('default', { month: 'long' });
            monthData[month] = (monthData[month] || 0) + 1;
        });

        new Chart(histCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(monthData),
                datasets: [{
                    label: 'Monthly Visitors',
                    data: Object.values(monthData),
                    backgroundColor: '#4CAF50'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// User Management Functions
function loadUsers() {
    const users = dataManager.getAll('users');
    const container = document.getElementById('usersTableBody');

    if (container) {
        container.innerHTML = '';
        users.forEach(user => {
            const row = document.createElement('div');
            row.className = 'table-rowuser';
            row.innerHTML = `
                <div class="name-cell" data-label="Name">${user.name}</div>
                <div data-label="Email">${user.email}</div>
                <div data-label="Role">${user.role_name}</div>
                <div class="actions" data-label="Actions">
                    <button class="edit-btn" onclick="editUser(${user.id})" aria-label="Edit user">Edit</button>
                    <button class="delete-btn" onclick="deleteUser(${user.id})" aria-label="Delete user">Delete</button>
                </div>
            `;
            container.appendChild(row);
        });
    }
}

function addUser(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);

    const userData = {
        name: formData.get('name'),
        email: formData.get('email'),
        role_name: formData.get('role_id') === '1' ? 'Administrator' :
                   formData.get('role_id') === '2' ? 'Manager' : 'Staff'
    };

    dataManager.add('users', userData);
    loadUsers();
    form.reset();

    // Close modal if using Bootstrap modal
    const modal = document.getElementById('myModal');
    if (modal && window.bootstrap) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) bsModal.hide();
    }

    alert('User added successfully!');
}

function editUser(id) {
    const user = dataManager.getById('users', id);
    if (user) {
        const newName = prompt('Enter new name:', user.name);
        const newEmail = prompt('Enter new email:', user.email);

        if (newName && newEmail) {
            dataManager.update('users', id, { name: newName, email: newEmail });
            loadUsers();
            alert('User updated successfully!');
        }
    }
}

function deleteUser(id) {
    if (confirm('Are you sure you want to delete this user?')) {
        dataManager.delete('users', id);
        loadUsers();
        alert('User deleted successfully!');
    }
}

// Province Management Functions
function loadProvinces() {
    const provinces = dataManager.getAll('provinces');
    const container = document.getElementById('provincesTableBody');

    if (container) {
        container.innerHTML = '';
        provinces.forEach((province, index) => {
            const row = document.createElement('div');
            row.className = 'table-rowspro';
            row.innerHTML = `
                <div data-label="SNO">${index + 1}</div>
                <div data-label="Province Name">${province.province_name}</div>
                <div data-label="Actions">
                    <button class="delete-province-btn btn btn-danger" onclick="deleteProvince(${province.id})" aria-label="Delete Province">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(row);
        });
    }
}

function addProvince(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);

    const provinceName = formData.get('province_name');
    if (!provinceName) {
        alert('Please enter province name');
        return;
    }

    const provinceData = {
        province_name: provinceName
    };

    dataManager.add('provinces', provinceData);
    loadProvinces();
    form.reset();
    alert('Province added successfully!');
}

function deleteProvince(id) {
    if (confirm('Are you sure you want to delete this province?')) {
        dataManager.delete('provinces', id);
        loadProvinces();
        alert('Province deleted successfully!');
    }
}

// City Management Functions
function loadCities() {
    const cities = dataManager.getAll('cities');
    const provinces = dataManager.getAll('provinces');
    const container = document.getElementById('citiesTableBody');

    if (container) {
        container.innerHTML = '';
        cities.forEach((city, index) => {
            const row = document.createElement('div');
            row.className = 'table-rowcity';
            row.innerHTML = `
                <div data-label="SNO">${index + 1}</div>
                <div data-label="City Name">${city.city_name}</div>
                <div data-label="Province Name">${city.province_name}</div>
                <div data-label="Actions">
                    <button class="delete-city-btn btn btn-danger" onclick="deleteCity(${city.id})" aria-label="Delete city">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(row);
        });
    }

    // Load provinces in dropdown
    const provinceSelect = document.querySelector('select[name="province_id"]');
    if (provinceSelect) {
        provinceSelect.innerHTML = '<option>Select</option>';
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.province_name;
            provinceSelect.appendChild(option);
        });
    }
}

function addCity(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);

    const cityName = formData.get('city_name');
    const provinceId = formData.get('province_id');

    if (!cityName || !provinceId) {
        alert('Please fill all fields');
        return;
    }

    const province = dataManager.getById('provinces', provinceId);
    const cityData = {
        city_name: cityName,
        province_id: parseInt(provinceId),
        province_name: province.province_name
    };

    dataManager.add('cities', cityData);
    loadCities();
    form.reset();
    alert('City added successfully!');
}

function deleteCity(id) {
    if (confirm('Are you sure you want to delete this city?')) {
        dataManager.delete('cities', id);
        loadCities();
        alert('City deleted successfully!');
    }
}

// Entry Management Functions
function loadEntries() {
    const entries = dataManager.getAll('entries');
    const container = document.getElementById('entriesTableBody');

    if (container) {
        container.innerHTML = '';
        entries.forEach(entry => {
            const row = document.createElement('div');
            row.className = 'table-rowentry';
            row.innerHTML = `
                <div data-label="DATE">${entry.created_at}</div>
                <div data-label="NAME">${entry.name}</div>
                <div data-label="F-NAME">${entry.father_name}</div>
                <div data-label="CNIC">${entry.cnic_no}</div>
                <div data-label="MOBILE">${entry.mobile_no}</div>
                <div data-label="CAST">${entry.cast}</div>
                <div data-label="PROFESSION">${entry.profession}</div>
                <div data-label="CITY">${entry.city_name}</div>
                <div data-label="PROVINCE">${entry.province_name}</div>
                <div data-label="ROOM">${entry.room_name}</div>
                <div data-label="ASSIGNED BED">${entry.bed_name || 'family'}</div>
                <div data-label="CHECK IN">${entry.check_in}</div>
                <div data-label="CHECK OUT">
                    ${entry.check_out ? entry.check_out :
                      `<button class="delete-btn checkOutBtn" onclick="checkOut(${entry.id})" style="padding: 6px 12px;">Check Out</button>`}
                </div>
                <div data-label="STATUS">${entry.status}</div>
            `;
            container.appendChild(row);
        });
    }
}

function checkOut(entryId) {
    if (confirm('Are you sure you want to check out?')) {
        const now = new Date().toLocaleString();
        dataManager.update('entries', entryId, {
            check_out: now,
            status: 'checked_out'
        });

        // Update bed status to available
        const entry = dataManager.getById('entries', entryId);
        if (entry && entry.bed_ids) {
            const bedIds = entry.bed_ids.split(',');
            bedIds.forEach(bedId => {
                dataManager.update('beds', parseInt(bedId), { status: 'available' });
            });
        }

        loadEntries();
        loadDashboard(); // Refresh dashboard stats
        alert('Checked out successfully!');
    }
}

// Room Management Functions
function loadRooms() {
    const rooms = dataManager.getAll('rooms');
    const container = document.getElementById('roomsTableBody');

    if (container) {
        container.innerHTML = '';
        rooms.forEach((room, index) => {
            const row = document.createElement('div');
            row.className = 'table-rowes';
            row.innerHTML = `
                <div data-label="SNO">${index + 1}</div>
                <div data-label="Room">${room.room_name}</div>
                <div data-label="Beds">${room.room_beds}</div>
                <div data-label="Status">${room.status}</div>
                <div class="actions" data-label="Actions">
                    <button class="update-room-btn btn btn-primary" onclick="editRoom(${room.id})" aria-label="update room">
                        <i class="fa fa-pencil"></i>
                    </button>
                    <button class="delete-room-btn btn btn-danger" onclick="deleteRoom(${room.id})" aria-label="Delete room">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(row);
        });
    }
}

function addRoom(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);

    const roomName = formData.get('room_name');
    const roomBeds = formData.get('room_beds');

    if (!roomName || !roomBeds) {
        alert('Please fill all fields');
        return;
    }

    const roomData = {
        room_name: roomName,
        room_beds: parseInt(roomBeds),
        status: 'available'
    };

    const newRoom = dataManager.add('rooms', roomData);

    // Create beds for the room
    for (let i = 1; i <= parseInt(roomBeds); i++) {
        const bedData = {
            bed_name: `Bed ${i}`,
            room_name: roomName,
            room_id: newRoom.id,
            status: 'available'
        };
        dataManager.add('beds', bedData);
    }

    loadRooms();
    form.reset();
    alert('Room added successfully!');
}

function editRoom(id) {
    const room = dataManager.getById('rooms', id);
    if (room) {
        const newName = prompt('Enter new room name:', room.room_name);
        if (newName) {
            dataManager.update('rooms', id, { room_name: newName });
            loadRooms();
            alert('Room updated successfully!');
        }
    }
}

function deleteRoom(id) {
    if (confirm('Are you sure you want to delete this room?')) {
        dataManager.delete('rooms', id);
        // Also delete associated beds
        const beds = dataManager.getAll('beds');
        beds.filter(bed => bed.room_id === id).forEach(bed => {
            dataManager.delete('beds', bed.id);
        });
        loadRooms();
        alert('Room deleted successfully!');
    }
}

// Reports Functions
function loadReports() {
    // Initialize report filters
    const cities = dataManager.getAll('cities');
    const provinces = dataManager.getAll('provinces');

    const citySelect = document.getElementById('fcity');
    const provinceSelect = document.getElementById('fprovince');

    if (citySelect) {
        citySelect.innerHTML = '<option value="">--Select City--</option>';
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.city_name;
            citySelect.appendChild(option);
        });
    }

    if (provinceSelect) {
        provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.province_name;
            provinceSelect.appendChild(option);
        });
    }
}

function searchData() {
    const fromDate = document.getElementById('from').value;
    const toDate = document.getElementById('to').value;
    const cityId = document.getElementById('fcity').value;
    const provinceId = document.getElementById('fprovince').value;
    const month = document.getElementById('month').value;

    let entries = dataManager.getAll('entries');

    // Filter by date range
    if (fromDate && toDate) {
        entries = entries.filter(entry => {
            const entryDate = new Date(entry.created_at);
            return entryDate >= new Date(fromDate) && entryDate <= new Date(toDate);
        });
    }

    // Filter by city
    if (cityId) {
        const city = dataManager.getById('cities', cityId);
        entries = entries.filter(entry => entry.city_name === city.city_name);
    }

    // Filter by province
    if (provinceId) {
        const province = dataManager.getById('provinces', provinceId);
        entries = entries.filter(entry => entry.province_name === province.province_name);
    }

    // Filter by month
    if (month) {
        entries = entries.filter(entry => {
            const entryMonth = new Date(entry.created_at).toLocaleString('default', { month: 'long' });
            return entryMonth === month;
        });
    }

    // Display results
    const tableBody = document.querySelector('#reportTable tbody');
    if (tableBody) {
        tableBody.innerHTML = '';
        entries.forEach(entry => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${entry.name}</td>
                <td>${entry.father_name}</td>
                <td>${entry.cnic_no}</td>
                <td>${entry.mobile_no}</td>
                <td>${entry.cast}</td>
                <td>${entry.profession}</td>
                <td>${entry.province_name}</td>
                <td>${entry.city_name}</td>
            `;
            tableBody.appendChild(row);
        });
    }
}

// Messages Functions
function loadMessages() {
    const entries = dataManager.getAll('entries');
    const tableBody = document.querySelector('#messages tbody');

    if (tableBody) {
        tableBody.innerHTML = '';
        entries.forEach(entry => {
            const row = document.createElement('tr');
            row.className = 'unique-tr';
            row.innerHTML = `
                <td class="unique-td">${entry.name}</td>
                <td class="unique-td">${entry.mobile_no}</td>
                <td class="unique-td"><input type="checkbox" class="unique-rowCheckbox"></td>
            `;
            tableBody.appendChild(row);
        });
    }
}

function sendMessage() {
    const message = document.getElementById('message').value;
    const selectedCheckboxes = document.querySelectorAll('.unique-rowCheckbox:checked');

    if (!message) {
        alert('Please enter a message');
        return;
    }

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one contact');
        return;
    }

    alert(`Message "${message}" sent to ${selectedCheckboxes.length} contacts!`);
    document.getElementById('message').value = '';

    // Uncheck all checkboxes
    document.querySelectorAll('.unique-rowCheckbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAll').checked = false;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set current user name
    document.getElementById('currentUser').textContent = 'Admin User';
    document.getElementById('sidebarUserName').textContent = 'Admin User';

    // Update date/time every second
    setInterval(updateDateTime, 1000);

    // Load dashboard by default
    showPage('dashboard');

    // Setup form event listeners
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', addUser);
    }

    const provinceForm = document.getElementById('provinceForm');
    if (provinceForm) {
        provinceForm.addEventListener('submit', addProvince);
    }

    const cityForm = document.getElementById('cityForm');
    if (cityForm) {
        cityForm.addEventListener('submit', addCity);
    }

    const roomForm = document.getElementById('roomForm');
    if (roomForm) {
        roomForm.addEventListener('submit', addRoom);
    }

    // Setup select all checkbox for messages
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.unique-rowCheckbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });
    }
});
