<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cities - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="true" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse show">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3 active" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-building me-2"></i>City Management</h1>
          </div>

          <!-- Main Content -->
          <div class="container">
            <div class="row">
              <!-- Cities Table -->
              <div class="col-md-8">
                <div class="table-section">
                  <h2>Cities</h2>
                  <div class="table-container">
                    <div class="table-headercity">
                      <div>SNO</div>
                      <div>City Name</div>
                      <div>Province Name</div>
                      <div>Actions</div>
                    </div>

                    <div id="citiesTableBody">
                      <!-- Cities will be loaded dynamically -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- Add City Form -->
              <div class="col-md-4">
                <div class="form-section">
                  <form class="card" id="cityForm" onsubmit="addCity(event)">
                    <div class="card-header">
                      <h3 class="card-title">Add City</h3>
                    </div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label class="form-label required">City Name</label>
                        <input type="text" name="city_name" class="form-control" placeholder="City Name" required>
                      </div>
                      <div class="mb-3">
                        <label class="form-label required">Province</label>
                        <select class="form-control" name="province_id" required>
                          <option>Select</option>
                          <!-- Provinces will be loaded dynamically -->
                        </select>
                      </div>
                    </div>
                    <div class="card-footer text-end">
                      <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the cities page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadCities();
      loadProvincesDropdown();
    });

    function loadCities() {
      const cities = dataManager.getAll('cities');
      const container = document.getElementById('citiesTableBody');

      if (container) {
        container.innerHTML = '';
        cities.forEach((city, index) => {
          const row = document.createElement('div');
          row.className = 'table-rowcity';
          row.innerHTML = `
            <div data-label="SNO">${index + 1}</div>
            <div data-label="City Name">${city.city_name}</div>
            <div data-label="Province Name">${city.province_name}</div>
            <div data-label="Actions">
              <button class="delete-city-btn btn btn-danger" onclick="deleteCity(${city.id})" aria-label="Delete city">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          `;
          container.appendChild(row);
        });
      }
    }

    function loadProvincesDropdown() {
      const provinces = dataManager.getAll('provinces');
      const provinceSelect = document.querySelector('select[name="province_id"]');

      if (provinceSelect) {
        provinceSelect.innerHTML = '<option>Select</option>';
        provinces.forEach(province => {
          const option = document.createElement('option');
          option.value = province.id;
          option.textContent = province.province_name;
          provinceSelect.appendChild(option);
        });
      }
    }

    function addCity(event) {
      event.preventDefault();
      const form = event.target;
      const formData = new FormData(form);

      const cityName = formData.get('city_name');
      const provinceId = formData.get('province_id');

      if (!cityName || !provinceId) {
        alert('Please fill all fields');
        return;
      }

      const province = dataManager.getById('provinces', provinceId);
      const cityData = {
        city_name: cityName,
        province_id: parseInt(provinceId),
        province_name: province.province_name
      };

      dataManager.add('cities', cityData);
      loadCities();
      form.reset();
      alert('City added successfully!');
    }

    function deleteCity(id) {
      if (confirm('Are you sure you want to delete this city?')) {
        dataManager.delete('cities', id);
        loadCities();
        alert('City deleted successfully!');
      }
    }
  </script>
</body>
</html>
