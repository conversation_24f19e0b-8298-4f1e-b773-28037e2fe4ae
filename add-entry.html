<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Add Entry - Darga</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>
<style>
  body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
  }

  input.form-control, select.form-select {
    border: none;
    border-bottom: 1px solid black;
    border-radius: 0;
    outline: none;
    box-shadow: none;
    font-size: 14px;
  }

  input.form-control:focus, select.form-select:focus {
    border: none;
    border-bottom: 1px solid black;
    outline: none;
    box-shadow: none;
  }

  .form-container {

  }

  .form-title {
    color: #4CAF50;
    font-weight: bold;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
  }

  .form-control:focus, .form-select:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
  }

  .btn-primary {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  }

  .btn-secondary {
    border-radius: 8px;
    padding: 0.75rem 2rem;
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .back-btn {
    color: white;
    text-decoration: none;
    font-weight: bold;
  }

  .back-btn:hover {
    color: #f8f9fa;
    text-decoration: none;
  }

  #video {
    border: 2px solid #4CAF50;
    border-radius: 8px;
  }

  .camera-section {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 1rem 0;
  }

  .visitor-type {
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
    margin: 1rem 0;
  }

  .visitor-type label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
  }

  .group-section {
    background: #e8f5e8;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
  }
</style>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="settings.html" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-plus-circle me-2"></i>Add New Entry</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <a href="index.html" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
              </a>
            </div>
          </div>

          <!-- Main Content -->
    <div class="form-container">
      <h2 class="form-title">
        <i class="fas fa-user-plus me-2"></i>Guest Entry Form
      </h2>

      <form id="addEntryForm" onsubmit="addEntry(event)">
        <!-- Group Checkbox -->
        <div class="group-section">
          <div class="form-check">
            <input type="checkbox" name="family" id="family_checkbox" class="form-check-input" value="0">
            <label for="family_checkbox" class="form-check-label fw-bold">
              <i class="fas fa-users me-2"></i>Group Entry
            </label>
          </div>

          <div class="col-md-6 group_members mt-3" style="display: none;">
            <label for="group_members" class="form-label">Group Members</label>
            <input type="text" class="form-control" id="group_members" name="group_members" placeholder="Enter number of group members">
          </div>
        </div>

        <div class="row g-3">
          <!-- CNIC -->
          <div class="col-md-6">
            <label for="cnic" class="form-label">
              <i class="fas fa-id-card me-2"></i>CNIC No.
            </label>
            <input type="text" class="form-control" id="cnic" autocomplete="off" name="cnic_no" placeholder="12345-1234567-1" required>
            <ul class="list-group" id="cnic-suggestions" style="position:absolute; z-index: 1000;"></ul>
          </div>

          <!-- Name -->
          <div class="col-md-6">
            <label for="name" class="form-label">
              <i class="fas fa-user me-2"></i>Full Name
            </label>
            <input type="text" class="form-control" id="name" name="name" required>
          </div>

          <!-- Father Name -->
          <div class="col-md-6">
            <label for="fatherName" class="form-label">
              <i class="fas fa-male me-2"></i>Father Name
            </label>
            <input type="text" class="form-control" id="fatherName" name="father_name" required>
          </div>

          <!-- Mobile -->
          <div class="col-md-6">
            <label for="mobile" class="form-label">
              <i class="fas fa-phone me-2"></i>Mobile No.
            </label>
            <input type="text" class="form-control" id="mobile" name="mobile_no" placeholder="03XX-XXXXXXX" required>
          </div>

          <!-- Cast -->
          <div class="col-md-6">
            <label for="cast" class="form-label">
              <i class="fas fa-users me-2"></i>Cast
            </label>
            <input type="text" class="form-control" id="cast" name="cast" required>
          </div>

          <!-- Profession -->
          <div class="col-md-6">
            <label for="profession" class="form-label">
              <i class="fas fa-briefcase me-2"></i>Profession
            </label>
            <input type="text" class="form-control" id="profession" name="profession" required>
          </div>

          <!-- Province -->
          <div class="col-md-6">
            <label for="province" class="form-label">
              <i class="fas fa-map me-2"></i>Province
            </label>
            <select class="form-control form-select" id="get_province" name="province_id" required>
              <option value="">Select Province</option>
              <!-- Provinces will be loaded dynamically -->
            </select>
          </div>

          <!-- City -->
          <div class="col-md-6">
            <label for="city" class="form-label">
              <i class="fas fa-city me-2"></i>City Name
            </label>
            <select class="form-control form-select" id="city_get" name="city_id" required>
              <option value="">Select City</option>
              <!-- Cities will be loaded here dynamically -->
            </select>
          </div>

          <!-- Room -->
          <div class="col-md-6">
            <label for="roomNo" class="form-label">
              <i class="fas fa-door-open me-2"></i>Room No.
            </label>
            <select class="form-control form-select" name="room_id" id="roomSelect" required>
              <option value="">Assign Room</option>
              <!-- Rooms will be loaded dynamically -->
            </select>
          </div>

          <!-- Bed Assignment -->
          <div class="col-md-6 room_beds" style="display: none;">
            <label for="bed_id" class="form-label">
              <i class="fas fa-bed me-2"></i>Bed Assignment
            </label>
            <select class="form-control form-select" name="bed_id">
              <option value="">Select Bed</option>
              <!-- Beds will be loaded dynamically -->
            </select>
          </div>

          <!-- Visitor Type -->
          <div class="col-12">
            <label class="form-label">
              <i class="fas fa-walking me-2"></i>Visitor Type
            </label>
            <div class="visitor-type">
              <label>
                <input type="radio" id="walkin_visitors" value="walkin_visitors" name="visitors" required>
                <i class="fas fa-walking me-1"></i>Walk-in Visitor
              </label>
              <label>
                <input type="radio" id="stay_visitors" value="stay_visitors" name="visitors" required>
                <i class="fas fa-bed me-1"></i>Stay Visitor
              </label>
            </div>
          </div>

          <!-- Status -->
          <div class="col-md-6">
            <label for="status" class="form-label">
              <i class="fas fa-info-circle me-2"></i>Status
            </label>
            <input type="text" class="form-control" id="status" name="status" value="Active" required>
          </div>

          <!-- Check In (for stay visitors) -->
          <div class="col-md-6 stay_inputs" style="display: none;">
            <label for="checkIn" class="form-label">
              <i class="fas fa-calendar-check me-2"></i>Check In
            </label>
            <input type="datetime-local" class="form-control" id="checkIn" name="check_in">
          </div>

          <!-- Camera Section -->
          <div class="col-12">
            <div class="camera-section">
              <label class="form-label d-block">
                <i class="fas fa-camera me-2"></i>Capture Photo
              </label>
              <video id="video" width="300" height="200" autoplay></video>
              <canvas id="canvas" width="300" height="200" style="display:none;"></canvas>
              <input type="hidden" name="captured_image" id="captured_image_input">
              <br>
              <button type="button" id="capture_btn" class="btn btn-sm btn-primary mt-2">
                <i class="fa fa-camera me-2"></i>Capture Photo
              </button>
              <div id="snapshot_preview" class="mt-2"></div>
            </div>
          </div>

          <!-- Remarks -->
          <div class="col-12">
            <label for="remarks" class="form-label">
              <i class="fas fa-comment me-2"></i>Remarks
            </label>
            <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Enter any additional remarks..."></textarea>
          </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mt-4">
          <button type="submit" class="btn btn-primary me-3">
            <i class="fas fa-save me-2"></i>Save Entry
          </button>
          <a href="index.html" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Cancel
          </a>
        </div>
      </form>
    </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    // Initialize the add entry page
    document.addEventListener('DOMContentLoaded', function() {
      // Set current user name
      document.getElementById('currentUser').textContent = 'Admin User';
      document.getElementById('sidebarUserName').textContent = 'Admin User';

      // Update date/time every second
      updateDateTime();
      setInterval(updateDateTime, 1000);

      loadProvinces();
      loadRooms();
      initializeCamera();
      setupFormHandlers();
    });

    function updateDateTime() {
      const dateTimeElement = document.getElementById('currentDateTime');
      if (dateTimeElement) {
        dateTimeElement.textContent = formatDateTime();
      }
    }

    function formatDateTime(date = new Date()) {
      return date.toLocaleString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      });
    }

    // Sidebar toggle
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebarMenu');
      sidebar.classList.toggle('active');
    }

    // Logout function
    function logout() {
      if (confirm('Are you sure you want to logout?')) {
        alert('Logged out successfully!');
        window.location.href = 'index.html';
      }
    }

    function loadProvinces() {
      // Initialize dataManager if not available
      if (typeof dataManager === 'undefined') {
        window.dataManager = new DataManager();
      }
      const provinces = dataManager.getAll('provinces');
      const provinceSelect = document.getElementById('get_province');

      if (provinceSelect) {
        provinceSelect.innerHTML = '<option value="">Select Province</option>';
        provinces.forEach(province => {
          const option = document.createElement('option');
          option.value = province.id;
          option.textContent = province.province_name;
          provinceSelect.appendChild(option);
        });
      }
    }

    function loadRooms() {
      const rooms = dataManager.getAll('rooms');
      const roomSelect = document.getElementById('roomSelect');

      if (roomSelect) {
        roomSelect.innerHTML = '<option value="">Assign Room</option>';
        rooms.forEach(room => {
          const option = document.createElement('option');
          option.value = room.id;
          option.textContent = room.room_name;
          option.setAttribute('data-beds', room.room_beds);
          roomSelect.appendChild(option);
        });
      }
    }

    function setupFormHandlers() {
      // Province change handler
      document.getElementById('get_province').addEventListener('change', function() {
        const provinceId = this.value;
        const cities = dataManager.getAll('cities');
        const citySelect = document.getElementById('city_get');

        citySelect.innerHTML = '<option value="">Select City</option>';

        if (provinceId) {
          const provinceCities = cities.filter(city => city.province_id == provinceId);
          provinceCities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.city_name;
            citySelect.appendChild(option);
          });
        }
      });

      // Room change handler
      document.getElementById('roomSelect').addEventListener('change', function() {
        const roomId = this.value;
        const bedsContainer = document.querySelector('.room_beds');
        const bedSelect = document.querySelector('select[name="bed_id"]');

        if (roomId) {
          const beds = dataManager.getAll('beds');
          const roomBeds = beds.filter(bed => bed.room_id == roomId && bed.status === 'available');

          bedSelect.innerHTML = '<option value="">Select Bed</option>';
          roomBeds.forEach(bed => {
            const option = document.createElement('option');
            option.value = bed.id;
            option.textContent = bed.bed_name;
            bedSelect.appendChild(option);
          });

          bedsContainer.style.display = 'block';
        } else {
          bedsContainer.style.display = 'none';
        }
      });

      // Family checkbox handler
      document.getElementById('family_checkbox').addEventListener('change', function() {
        const groupMembersDiv = document.querySelector('.group_members');
        if (this.checked) {
          groupMembersDiv.style.display = 'block';
          this.value = '1';
        } else {
          groupMembersDiv.style.display = 'none';
          this.value = '0';
        }
      });

      // Visitor type handler
      document.querySelectorAll('input[name="visitors"]').forEach(radio => {
        radio.addEventListener('change', function() {
          const stayInputs = document.querySelector('.stay_inputs');
          if (this.value === 'stay_visitors') {
            stayInputs.style.display = 'block';
            document.getElementById('checkIn').required = true;
          } else {
            stayInputs.style.display = 'none';
            document.getElementById('checkIn').required = false;
          }
        });
      });

      // CNIC formatting
      document.getElementById('cnic').addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^0-9]/g, '').slice(0, 13);
        let formatted = '';

        if (value.length > 5) {
          formatted += value.substring(0, 5) + '-';
          if (value.length > 12) {
            formatted += value.substring(5, 12) + '-' + value.substring(12);
          } else {
            formatted += value.substring(5, 12);
          }
        } else {
          formatted = value;
        }

        e.target.value = formatted;
      });

      // Mobile formatting
      document.getElementById('mobile').addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^0-9]/g, '').slice(0, 11);
        let formatted = '';

        if (value.length > 4) {
          formatted = value.substring(0, 4) + '-' + value.substring(4);
        } else {
          formatted = value;
        }

        e.target.value = formatted;
      });
    }

    function initializeCamera() {
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const captureBtn = document.getElementById('capture_btn');
      const capturedInput = document.getElementById('captured_image_input');

      // Start video stream
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          video.srcObject = stream;
        })
        .catch(err => {
          console.error('Error accessing camera: ', err);
          captureBtn.disabled = true;
          captureBtn.textContent = 'Camera not available';
        });

      captureBtn.addEventListener('click', () => {
        const context = canvas.getContext('2d');
        canvas.style.display = 'block';
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert to base64 image
        const imageData = canvas.toDataURL('image/png');
        capturedInput.value = imageData;

        // Show preview
        const preview = document.getElementById('snapshot_preview');
        preview.innerHTML = `<img src="${imageData}" style="width: 150px; height: 100px; border-radius: 8px; margin-top: 10px;">`;
      });
    }

    function addEntry(event) {
      event.preventDefault();
      const form = event.target;
      const formData = new FormData(form);

      // Get selected province and city names
      const provinceSelect = document.getElementById('get_province');
      const citySelect = document.getElementById('city_get');
      const roomSelect = document.getElementById('roomSelect');
      const bedSelect = document.querySelector('select[name="bed_id"]');

      const provinceName = provinceSelect.options[provinceSelect.selectedIndex]?.text || '';
      const cityName = citySelect.options[citySelect.selectedIndex]?.text || '';
      const roomName = roomSelect.options[roomSelect.selectedIndex]?.text || '';
      const bedName = bedSelect.options[bedSelect.selectedIndex]?.text || '';

      const entryData = {
        name: formData.get('name'),
        father_name: formData.get('father_name'),
        cnic_no: formData.get('cnic_no'),
        mobile_no: formData.get('mobile_no'),
        cast: formData.get('cast'),
        profession: formData.get('profession'),
        city_name: cityName,
        province_name: provinceName,
        room_name: roomName,
        bed_name: bedName,
        check_in: formData.get('check_in') || new Date().toLocaleString(),
        check_out: '',
        status: formData.get('status'),
        created_at: new Date().toLocaleString(),
        family: formData.get('family') === '1',
        group_members: formData.get('group_members') || '',
        room_id: parseInt(formData.get('room_id')),
        bed_ids: formData.get('bed_id') || '',
        visitors: formData.get('visitors'),
        remarks: formData.get('remarks'),
        captured_image: formData.get('captured_image')
      };

      // Add entry to database
      dataManager.add('entries', entryData);

      // Update bed status if bed is assigned
      if (entryData.bed_ids) {
        dataManager.update('beds', parseInt(entryData.bed_ids), { status: 'occupied' });
      }

      alert('Entry added successfully!');

      // Redirect back to main page
      window.location.href = 'index.html';
    }
  </script>
</body>
</html>
