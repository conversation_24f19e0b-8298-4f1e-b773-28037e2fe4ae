
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Darga</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">

  <!-- In <head> -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>

<!-- Before </body> -->
  <link rel="stylesheet" href="/style.css">
</head>
<style type="text/css">
      input.form-control, select.form-select {
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0;
        outline: none;
        box-shadow: none;
        font-size: 14px;
    }

    body {
        overflow-x: hidden;
    }

    input.form-control:focus, select.form-select:focus {
        border: none;
        border-bottom: 1px solid black;
        outline: none;
        box-shadow: none;
    }

    label {
        font-size: 10px;
        width: 150px;
    }

    .form-label {
        font-size: 12px;
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: pointer;
    }
       #mydiv{
         /* float: right; */
    position: absolute;
    margin-top: -13px;
    /* width: 170px; */
    /* height: 100px; */
    margin-left: 689px;
    }

    h1 {
      text-align: center;
      color: #333;
    }

    .rowreport {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      margin-top: 20px;
    }

    .field {
      flex: 1;
      min-width: 200px;
      display: flex;
      flex-direction: column;
    }

    .field label {
      font-size: 12px;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }

    input.form-control, select.form-select {
      border: none;
      border-bottom: 1px solid black;
      border-radius: 0;
      outline: none;
      box-shadow: none;
      font-size: 14px;
    }

    input.form-control:focus, select.form-select:focus {
      border: none;
      border-bottom: 1px solid black;
      outline: none;
      box-shadow: none;
    }

    .form-label {
      font-size: 12px;
    }

    .form-control[readonly] {
      background-color: #f8f9fa;
      cursor: pointer;
    }

    .submit-btn {
      margin-top: 20px;
      padding: 12px 20px;
      background-color: #1eba41;
      color: #fff;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
    }

    .submit-btn:hover {
      background-color: #2980b9;
    }

    /* Table Styles */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 30px;
    }

    table, th, td {
      border: 1px solid #ddd;
    }

    th, td {
      padding: 10px;
      text-align: left;
    }

    th {
      background-color: #34db6c;
      color: white;
    }

    /* Responsive Table */
    .table-wrapper {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      margin-top: 20px;
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }

    .pagination button {
      padding: 8px 16px;
      margin: 0 5px;
      background-color: #1eba41;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    .pagination button:hover {
      background-color: green;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      .field {
        width: 100%;
      }

      .submit-btn {
        width: 100%;
      }
    }

    @media (max-width: 480px) {
      .container {
        width: 100%;
        padding: 15px;
      }

      .row {
        flex-direction: column;
        gap: 10px;
      }

      .field {
        width: 100%;
      }
    }

    .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after{
      font-size: 25px;
    }
    .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after{
      font-size: 25px;
    }

      .unique-table {
      border-collapse: collapse;
      width: 100%;
      margin: 0 auto;
      background-color: #fff;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .unique-th, .unique-td {
      border: 1px solid #ddd;
      padding: 12px 16px;
      text-align: center;
    }
    .unique-th {
      background-color: #4CAF50;
      color: white;
    }
    .unique-tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .unique-tr:hover {
      background-color: #f1f1f1;
    }
    .unique-caption {
      padding: 10px;
      font-size: 1.5em;
      font-weight: bold;
    }
    .unique-message-box {
      width: 100%;
      margin: 20px auto;
      display: flex;
      gap: 10px;
    }
    .unique-message-box input[type="text"] {
      flex: 1;
      padding: 12px 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      min-height: 45px;
    }
    .unique-message-box button {
      padding: 12px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .unique-message-box button:hover {
      background-color: #45a049;
    }
    #snapshot_preview img{
         height:87px;
    }
</style>
<!-- Update any blue buttons or elements to green -->
<style>
  /* Override Bootstrap's blue colors with green */
  .btn-primary {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
  }

  .btn-primary:hover,
  .btn-primary:focus,
  .btn-primary:active {
    background-color: #3e8e41 !important;
    border-color: #3e8e41 !important;
  }

  .btn-outline-primary {
    color: #4CAF50 !important;
    border-color: #4CAF50 !important;
  }

  .btn-outline-primary:hover,
  .btn-outline-primary:focus,
  .btn-outline-primary:active {
    background-color: #4CAF50 !important;
    color: white !important;
  }

  .text-primary {
    color: #4CAF50 !important;
  }

  .bg-primary {
    background-color: #4CAF50 !important;
  }

  .border-primary {
    border-color: #4CAF50 !important;
  }

  /* Links */
  a {
    color: #4CAF50;
  }

  a:hover {
    color: #3e8e41;
  }

  /* Form focus */
  .form-control:focus {
    border-color: #80c883 !important;
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25) !important;
  }

  /* Switch */
  .form-switch .form-check-input:checked {
    background-color: #4CAF50 !important;
    border-color: #4CAF50 !important;
  }
</style>
<body>
<!-- Add browser-like navigation bar at the very top -->


<!-- Add company bar below browser bar -->
<div class="company-navbar">
  <div class="company-info">
    <span id="currentUser">Admin User</span>
  </div>
  <div class="page-info">
    <span style="margin-right: 40px;" id="currentDateTime"></span>
    <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
  </div>
</div>


<!-- Keep the existing secondary navbar -->
<div class="container-fluid">
  <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
  <div class="row">
    <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
      <button class="close-btn" onclick="toggleSidebar()"|Earia-label="Close sidebar">×</button>
      <div class="position-sticky ">
        <div class="sidebar-header">
          <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
          <div class="user-info">
            <div class="user-name" id="sidebarUserName">Admin User</div>

            <a href="#" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
          </div>
        </div>
       <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('dashboard')" aria-current="page"><i class="bi bi-house"></i> Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
              <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
            </a>
            <div id="setupDropdown" class="collapse">
              <a class="nav-link ms-3" href="#" onclick="showPage('user')">User</a>
              <a class="nav-link ms-3" href="#" onclick="showPage('province')">Province</a>
              <a class="nav-link ms-3" href="#" onclick="showPage('city')">City</a>
            </div>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('addEntry')"><i class="bi bi-plus"></i> Add Entry</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('addRoom')"><i class="bi bi-door-open"></i> Add Room</a>
          </li>
            <!-- Room Cards -->
          <!--<li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('addBeds')"><i class="bi bi-plus"></i> Add Beds</a>
          </li>-->
          <li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('reports')"><i class="bi bi-files"></i> Reports</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" onclick="showPage('messages')"><i class="bi bi-files"></i> Message</a>
          </li>
        </ul>
      </div>
    </nav>
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
      <div class="pt-3">
        <div id="dashboard" class="page-section active">
          <div class="container mt-4">

           <!-- Action Boxes -->
            <div class="dashboard-actions">
              <div class="action-box">
                <i class="fas fa-shopping-cart"></i>
                <span>Entry</span>
              </div>
              <div class="action-box">
                <i class="fas fa-list"></i>
                <span>Visitors List</span>
              </div>
              <div class="action-box">
                <i class="fas fa-flag"></i>
                <span>Reports</span>
              </div>
              <div class="action-box">
                <i class="fas fa-chart-bar"></i>
                <span>Rooms</span>
              </div>
              <div class="action-box">
                <i class="fas fa-file-invoice"></i>
                <span>Beds</span>
              </div>
              <div class="action-box">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Messages</span>
              </div>
              <div class="action-box">
                <i class="fas fa-calendar-times"></i>
                <span>City</span>
              </div>
              <div class="action-box">
                <i class="fas fa-database"></i>
                <span>Users</span>
              </div>
              <div class="action-box">
                <i class="fas fa-store"></i>
                <span>Province</span>
              </div>
            </div>

            <!-- Top Cards -->
            <div class="container-fluid mt-4">
              <div class="miancards">
                <div class="dashboard">
                  <div class="card" style="background-color: #28a745;">
                    <h3>TOTAL INDIVIDUAL</h3>
                    <div class="number" id="totalIndividual">25</div>
                    <p>TOTAL INDIVIDUAL TODAY</p>
                    <div class="today" id="individualToday">5</div>
                    <i class="fas fa-user icon"></i>
                    <a href="#" class="details">Details</a>
                  </div>
                  <div class="card" style="background-color: #dc3545;">
                    <h3>TOTAL FAMILY</h3>
                    <div class="number" id="totalFamily">12</div>
                    <p>TOTAL FAMILY TODAY</p>
                    <div class="today" id="familyToday">3</div>
                    <i class="fas fa-users icon"></i>
                    <a href="#" class="details">Details</a>
                  </div>
                  <div class="card" style="background-color: #6f42c1;">
                    <h3>TOTAL ROOMS</h3>
                    <div class="number" id="totalRooms">10</div>
                    <p>TOTAL ROOMS AVAILABLE</p>
                    <div class="today" id="availableRooms">7</div>
                    <i class="fas fa-home icon"></i>
                    <a href="#" class="details">Details</a>
                  </div>
                  <div class="card" style="background-color: #fd7e14;">
                    <h3>TOTAL BEDS</h3>
                    <div class="number" id="totalBeds">50</div>
                    <p>TOTAL BEDS AVAILABLE</p>
                    <div class="today" id="availableBeds">35</div>
                    <i class="fas fa-bed icon"></i>
                    <a href="#" class="details">Details</a>
                  </div>
                </div>
              </div>
            </div>


        <div class="container my-5">
          <div class="row justify-content-center">
            <div class="col-12 col-xl-12">
              <div class="swiper mySwiper">
                <div class="swiper-wrapper" id="roomsContainer">
                  <!-- Rooms will be dynamically loaded here -->
                </div>

                <div class="d-flex justify-content-center gap-3 mt-3">
                  <div class="swiper-button-prev position-static"></div>
                  <div class="swiper-button-next position-static"></div>
                </div>
              </div>
            </div>
          </div>
        </div>






            <!-- Room Cards -->
           <!-- <div class="container my-5">
              <div class="row g-4">
                <div class="col-md-4">
                  <div class="room-card border rounded-4 p-4 text-center shadow">
                    <h5 class="fw-bold text-start">Room 1</h5>
                    <div class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745; "></i><p>Bed 1</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 2</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 3</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 4</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 5</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 6</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 7</p></div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="room-card border rounded-4 p-4 text-center shadow">
                    <h5 class="fw-bold text-start">Room 2</h5>
                    <div class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 1</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 2</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; "></i><p>Bed 3</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 4</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 5</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 6</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 7</p></div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="room-card border rounded-4 p-4 text-center shadow">
                    <h5 class="fw-bold text-start">Room 3</h5>
                    <div class="d-flex flex-wrap justify-content-center gap-2 mt-3">
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 1</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 2</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 3</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 4</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 5</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer; color:#28a745;"></i><p>Bed 6</p></div>
                      <div><i class="fas fa-bed" style="font-size: 50px; cursor:pointer;"></i><p>Bed 7</p></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>-->








            <!-- Table Section -->
            <div class="mb-4">
              <div class="dashboard-table-container">
                <div class="dashboard-table-header">
                  <h3>Recent Entries</h3>
                  <a href="add-entry.html" class="dashboard-add-btn" style="text-decoration: none; display: inline-block;">
                    <i class="fas fa-plus-circle"></i> Add Entry
                  </a>
                </div>
                <div class="dashboard-table-wrapper">
                  <table class="dashboard-table">
                    <thead>
                      <tr>
                        <th>Full Name</th>
                        <th>F. Name</th>
                        <th>CNIC</th>
                        <th>Mobile</th>
                        <th>Cast</th>
                        <th>Profession</th>
                      </tr>
                    </thead>
                    <tbody id="recentEntriesTable">
                      <!-- Recent entries will be loaded dynamically -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- Charts -->
            <div class="row">
              <div class="col-md-6">
                <div class="card chart-card">
                  <div class="card-body">
                    <h5 class="card-title">Pakistan Cities Guests</h5>
                    <div class="chart-container">
                      <canvas id="pieChart"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card chart-card">
                  <div class="card-body">
                    <h5 class="card-title">Monthly Visitors to Pakistan</h5>
                    <div class="chart-container">
                      <canvas id="histogram"></canvas>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="user" class="page-section">
          <div class="container">
            <div class="header">
              <h2>Users</h2>
              <button class="add-btn" data-bs-toggle="modal" data-bs-target="#myModal">+ Add New User</button>
            </div>
           <div class="table-container">
              <div class="table-headeruser">
                <div>Name</div>
                <div>Email</div>
                <div>Role</div>
                <div>Actions</div>
              </div>

              <div id="usersTableBody">
                <!-- Users will be loaded dynamically -->
              </div>
            </div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
        <div id="province" class="page-section">
          <div class="container">
            <div class="table-section">
                <h2>Provinces</h2>
                <div class="table-container">
                    <div class="table-headerpro">
                        <div>SNO</div>
                        <div>Province Name</div>
                        <div>Actions</div>
                    </div>

                    <div id="provincesTableBody">
                        <!-- Provinces will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="form-section">
                <!-- Updated Province form to match City design -->
                <form class="card" id="provinceForm" onsubmit="addProvince(event)">
                    <div class="card-header">
                        <h3 class="card-title">Add Province</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label required">Province Name</label>
                            <div>
                                <input type="text" name="province_name" class="form-control" placeholder="Province Name">
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </form>
            </div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
        <div id="city" class="page-section">
          <div class="container">
         <div class="table-section">
            <h2>Cities</h2>
            <div class="table-container">
                <div class="table-headercity">
                    <div>SNO</div>
                    <div>City Name</div>
                    <div>Province Name</div>
                    <div>Actions</div>
                </div>

                <div id="citiesTableBody">
                    <!-- Cities will be loaded dynamically -->
                </div>
            </div>
        </div>
            <div class="form-section">

                 <form class="card" id="cityForm" onsubmit="addCity(event)">
                <div class="card-header">
                    <h3 class="card-title">Add Cities</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label required">City Name</label>
                        <div>
                            <input type="text" name="city_name" class="form-control"  placeholder="City Name">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label required">Select Province</label>
                        <select class="form-control" name="province_id">
                            <option>Select</option>
                            <!-- Provinces will be loaded dynamically -->
                        </select>
                    </div>

                </div>
                <div class="card-footer text-end">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </form>
            </div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
        <div id="addEntry" class="page-section">
          <div class="container">
            <div class="header">
              <h2>Entries</h2>
              <a href="add-entry.html" class="add-btn" style="text-decoration: none; display: inline-block;">+ Add Entry</a>
            </div>
            <div class="form-group">
              <label for="search-number">Search Number:</label>
              <input type="text" id="search-number" placeholder="Enter number">
            </div>
            <div class="table-container">
            <div class="table-headerentry">
    <div>DATE</div>
    <div>NAME</div>
    <div>F-NAME</div>
    <div>CNIC</div>
    <div>MOBILE</div>
    <div>CAST</div>
    <div>PROFESSION</div>
    <div>CITY</div>
    <div>PROVINCE</div>
    <div>ROOM</div>
    <div>ASSIGNED BED</div>
    <div>CHECK IN</div>
    <div>CHECK OUT</div>
    <div>STATUS</div>
</div>

<div id="entriesTableBody">
    <!-- Entries will be loaded dynamically -->
</div>

</div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
        <div id="addRoom" class="page-section">
          <div class="container">
            <div class="table-section">
              <h2>Rooms</h2>
            <div class="table-container">
                <div class="table-headers">
                    <div>SNO</div>
                    <div>Room</div>
                    <div>Beds</div>
                    <div>Status</div>
                    <div>Actions</div>
                </div>

                <div id="roomsTableBody">
                    <!-- Rooms will be loaded dynamically -->
                </div>
            </div>
            </div>
            <div class="form-section">

                <form id="roomForm" class="card" onsubmit="addRoom(event)">
                <div class="card-header">
                  <h3 class="card-title">Add Rooms</h3>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label class="form-label required">Rooms</label>
                    <div>
                      <input type="text" name="room_name" class="form-control"  placeholder="Room Name">
                    </div>
                  </div>
                            <div class="mb-3">
                                <label class="form-label required">Beds</label>
                                <div>
                                    <input type="number" name="room_beds" class="form-control"  placeholder="Room Beds">
                                </div>
                            </div>

                </div>
                <div class="card-footer text-end">
                  <button type="submit" class="btn btn-primary">Submit</button>
                </div>
              </form>
            </div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
        <div id="addBeds" class="page-section">
          <div class="container">
            <div class="table-section">
              <h2>Beds</h2>
             <div class="table-container">
                <div class="table-headerbed">
                    <div>SNO</div>
                    <div>Beds</div>
                    <div>Room</div>
                    <div>Actions</div>
                </div>

                <?php $sno = 1; ?>
                @foreach($beds as $bed)
                    <div class="table-rowbed">
                        <div data-label="SNO">{{ $sno++ }}</div>
                        <div data-label="Beds">{{ $bed->bed_name }}</div>
                        <div data-label="Room">{{ $bed->room_name }}</div>
                        <div data-label="Actions">
                            <button class="delete-btn" aria-label="Delete bed"><i class="fa fa-trash"></i></button>
                        </div>
                    </div>
                @endforeach
</div>
            </div>
            <div class="form-section">
              <h3>Beds</h3>
               <form  id="bedForm"  class="card" method="POST" action="{{route('beds.store')}}">
                @csrf
        <div class="card-header">
          <h3 class="card-title">Beds</h3>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label required">Bed Name</label>
            <div>
              <input type="text" name="bed_name" class="form-control"  placeholder="Bed Name">
            </div>
          </div>
                    <div class="mb-3">
                        <label class="form-label required">Select Room</label>
                        <select class="form-control" name="room_id">
                                @foreach($rooms2 as $room)
                                    <option value="{{$room->id}}">{{$room->room_name}}</option>
                                @endforeach
                        </select>
                    </div>

        </div>
        <div class="card-footer text-end">
          <button type="submit" class="btn btn-primary">Submit</button>
        </div>
      </form>
            </div>
            <div class="footer">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </div>


       <div id="reports" class="page-section">
           <div class="container">
              <h1>Reports</h1>

              <div class="row">
                <div class="field">
                  <label for="from">From</label>
                  <input type="date" class="form-control" id="from">
                </div>

                <div class="field">
                  <label for="to">To</label>
                  <input type="date" class="form-control" id="to">
                </div>

                <div class="field">
                  <label for="city">City</label>
                     <select id="fcity" class="form-select">
                    <option value="">--Select City--</option>
                    <!-- Cities will be loaded dynamically -->
                  </select>
                </div>

                <div class="field">
                  <label for="province">Province</label>
                    <select id="fprovince" class="form-select">
                    <option value="">--Select Province--</option>
                    <!-- Provinces will be loaded dynamically -->
                  </select>
                </div>

                <div class="field">
                  <label for="month">Select Month</label>
                  <select id="month" class="form-select">
                    <option value="">--Select Month--</option>
                    <option value="January">January</option>
                    <option value="February">February</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="August">August</option>
                    <option value="September">September</option>
                    <option value="October">October</option>
                    <option value="November">November</option>
                    <option value="December">December</option>
                  </select>
                </div>
              </div>

              <button class="submit-btn" onclick="searchData()">Search</button>

              <!-- Responsive Table Wrapper -->
              <div class="table-wrapper" id="tableWrapper">
                  <button id="exportCSV" class="btn btn-primary"><i class="fas fa-file-csv"></i></button>
                <button id="exportExcel" class="btn btn-primary"><i class="fas fa-file-excel"></i></button>
                <button id="exportPDF" class="btn btn-primary"><i class="fas fa-file-pdf"></i></button>


                <table id="reportTable">
                  <thead>
                    <tr>
                      <th>Full Name</th>
                      <th>Father Name</th>
                      <th>Cnic</th>
                      <th>Mobile Numer</th>
                      <th>Cast</th>
                      <th>Profession</th>
                      <th>Province</th>
                      <th>City</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be populated dynamically after search -->
                  </tbody>
                </table>
              </div>

              <!-- Pagination Controls -->


            </div>
              <div class="pagination" id="pagination">
                <button onclick="changePage(-1)">Previous</button>
                <button onclick="changePage(1)">Next</button>
              </div>

          </div>


           <div id="messages" class="page-section">
            <div class="container">
                <h1>Messages</h1>
                  <table class="unique-table">
                        <caption class="unique-caption">Contact List</caption>
                        <thead>
                          <tr class="unique-tr">
                            <th class="unique-th">Name</th>
                            <th class="unique-th">Number</th>
                            <th class="unique-th"><input type="checkbox" id="selectAll" class="unique-selectAll"></th>
                          </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                          <!-- Messages will be loaded dynamically -->
                        </tbody>
                      </table>

                      <div class="unique-message-box">
                        <input type="text" id="message" placeholder="Type your message here..." class="unique-message-input">
                        <button onclick="sendMessage()" class="unique-submit-button">Submit</button>
                      </div>
              </div>
          </div>


            </main>
          </div>
        </div>




<!-- Add user Modal -->

<!-- The Modal -->
<div class="modal" id="myModal">
  <div class="modal-dialog">
    <div class="modal-content">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Add Users</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>

      <!-- Modal body -->
       <div class="modal-body">
                <form id="addUserForm" class="card" onsubmit="addUser(event)">
                    <div class="card-body">
                        <h3 class="card-title">Add New User</h3>
                        <div class="row row-cards">
                            <div class="col-sm-6 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" name="name" class="form-control" placeholder="Full Name" required>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email address</label>
                                    <input type="email" name="email" class="form-control" placeholder="Email" required>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                                </div>
                            </div>

                            <div class="col-sm-6 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">User Role</label>
                                    <select name="role_id" class="form-control" required>
                                        <option value="">Select Role</option>
                                        <option value="1">Administrator</option>
                                        <option value="2">Manager</option>
                                        <option value="3">Staff</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </form>

      </div>
      <!-- Modal footer -->
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
      </div>

    </div>
  </div>
</div>
<!-- Modal removed - Add Entry now redirects to separate page -->



<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script src="app.js"></script>
<script>
  function showPage(pageId) {
    document.querySelectorAll('.nav-link').forEach(link => {
      link.classList.remove('active');
    });

    // Add active class to the clicked nav link
    const clickedLink = document.querySelector(`.nav-link[onclick*="showPage('${pageId}')"]`);
    if (clickedLink) {
      clickedLink.classList.add('active');
    }

    // Show the selected page
    document.querySelectorAll('.page-section').forEach(page => {
      page.classList.remove('active');
    });
    document.getElementById(pageId).classList.add('active');
  }

  function toggleSidebar() {
    const sidebar = document.getElementById('sidebarMenu');
    sidebar.classList.toggle('active');
  }

  function showAddEntryForm() {
    alert('Add Entry form will be implemented here.');
    // Placeholder for future form implementation
  }

  document.addEventListener('DOMContentLoaded', () => {
    // Chart.js Initialization


  });
</script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<script>
    $(document).ready(function () {
        $('#addUserForm').on('submit', function (e) {
            e.preventDefault(); // Prevent default form submit

            let formData = new FormData(this);

            $.ajax({
                url: '{{ url("users/store") }}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
               success: function (response) {
                    if (response.success) {
                        toastr.success(response.message); // ✅ Show success toast
                        $('#addUserForm')[0].reset();
                        $('#myModal').hide();
                    } else {
                        toastr.error(response.message); // In case you return success: false
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        $.each(errors, function (key, value) {
                            $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);
                        });
                        toastr.error("Please correct the errors.");
                    } else {
                        toastr.error("Something went wrong.");
                    }
                }
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        $('#provinceForm').on('submit', function (e) {
            e.preventDefault();

            let formData = new FormData(this);

            $.ajax({
                url: '{{ route("province.store") }}',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function (response) {
                    if (response.success) {
                        toastr.success(response.message);
                        $('#provinceForm')[0].reset();
                    } else {
                        toastr.error(response.message || "An error occurred.");
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        $.each(errors, function (key, value) {
                            $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);
                        });
                        toastr.error("Please correct the errors.");
                    } else {
                        toastr.error("Something went wrong.");
                    }
                }
            });
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function () {
    $('#cityForm').on('submit', function (e) {
        e.preventDefault();  // Prevent the default form submit action

        let formData = new FormData(this);  // Create a new FormData object to easily send form data

        $.ajax({
            url: '{{ route("city.store") }}',  // URL to send the data to
            type: 'POST',  // HTTP method
            data: formData,  // Form data
            processData: false,  // Prevent jQuery from trying to process the data
            contentType: false,  // Prevent jQuery from setting content type
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'  // Include the CSRF token
            },
            success: function (response) {
                // Check if the response indicates success
                if (response.success) {
                    toastr.success(response.message);  // Show success toast notification
                    $('#cityForm')[0].reset();  // Reset the form
                } else {
                    toastr.error(response.message);  // Show error toast notification
                }
            },
            error: function (xhr) {
                // Handle errors (e.g., validation errors)
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);  // Display validation error
                    });
                    toastr.error("Please correct the errors.");  // Show general error toast
                } else {
                    toastr.error("Something went wrong.");
                }
            }
        });
    });
});

</script>

<script>
  $(document).ready(function () {
    $('#tableSearch').on('keyup', function () {
      var value = $(this).val().toLowerCase();
      $('table tbody tr').filter(function () {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
      });
    });

  });
</script>

<script>
    // Function to format date-time
    function formatDateTime(datetimeStr) {
        const date = new Date(datetimeStr);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
    }

    document.querySelectorAll('[id^="checkIn"]').forEach(function(cell) {
        const rawDate = cell.innerText;
        cell.innerText = formatDateTime(rawDate);
    });

    document.querySelectorAll('[id^="checkOut"]').forEach(function(cell) {
        const rawDate = cell.innerText;
        cell.innerText = formatDateTime(rawDate);
    });
</script>
<script>
  $(document).ready(function () {
    $("input[name='visitors']").change(function () {
      if ($("#stay_visitors").is(":checked")) {
        $(".stay_inputs").show();

      } else {
        $(".stay_inputs").hide();
      }
    });
  });
</script>
<script>
$(document).ready(function() {
    $('#get_province').on('change', function() {
        var provinceId = $(this).val();
        if (provinceId) {
            $.ajax({
                url: "{{ route('get.cities') }}",
                type: "GET",
                data: { province_id: provinceId },
                success: function(data) {
                    $('#city').empty().append('<option value="">Select City</option>');
                    $.each(data, function(key, city) {
                        $('#city_get').append('<option value="' + city.id + '">' + city.city_name + '</option>');
                    });
                }
            });
        } else {
            $('#city_get').empty().append('<option value="">Select City</option>');
        }
    });
});
</script>

<script>
$(document).ready(function() {
    $('#roomSelect').on('change', function() {
        var room_id = $(this).val();
        if (room_id) {
            $.ajax({
                url: "{{ route('get.beds') }}",
                type: "GET",
                data: { room_id: room_id },
                success: function(data) {
                    $('#beds_gets').empty().append('<option value="">Select Bed</option>');
                    $.each(data, function(key, beds) {
                        $('#beds_gets').append('<option value="' + beds.id + '">' + beds.bed_name + '</option>');
                    });
                }
            });
        } else {
            $('#city').empty().append('<option value="">Select City</option>');
        }
    });
});
</script>
<script>
$(document).ready(function () {
    $('#cnic').on('keyup', function () {
        let query = $(this).val();
        if (query.length >= 3) {
            $.ajax({
                url: "{{ route('search.cnic') }}",
                type: "GET",
                data: { term: query },
                success: function (data) {
                    let suggestionList = $('#cnic-suggestions');
                    suggestionList.empty();
                    if (data.length > 0) {
                        $.each(data, function (index, record) {
                            suggestionList.append(
                                `<li class="list-group-item list-group-item-action" data-record='${JSON.stringify(record)}'>${record.cnic_no}</li>`
                            );
                        });
                    } else {
                        suggestionList.append('<li class="list-group-item">No match found</li>');
                    }
                }
            });
        }
    });

    // Handle suggestion click
    $(document).on('click', '#cnic-suggestions li', function () {
        let record = $(this).data('record');
        $('#cnic').val(record.cnic_no);
        $('#name').val(record.name);
        $('#fatherName').val(record.father_name);
        $('#mobile').val(record.mobile_no);
        $('#cast').val(record.cast);
        $('#profession').val(record.profession);
        // $('#province').val(record.province_id).trigger('change'); // Load cities
        // $('#roomSelect').val(record.room_id);
        $('#room_beds').val(record.room_assign_beds);
        $('#status').val(record.status);
        $('#remarks').val(record.remarks);
        $('#checkIn').val(record.check_in.replace(' ', 'T'));
        $('#checkOut').val(record.check_out.replace(' ', 'T'));
        $('#cnic-suggestions').empty(); // Hide suggestions
    });

    // Hide suggestion on click outside
    $(document).click(function (e) {
        if (!$(e.target).closest('#cnic').length) {
            $('#cnic-suggestions').empty();
        }
    });
});
</script>
<script>
  $(document).ready(function() {
    $('#family_checkbox').change(function() {
      if ($(this).is(':checked')) {
        $('.room_beds').hide();
        $(this).val('1');
        $('.group_members').show();
      } else {
        $('.room_beds').show();
        $(this).val('0');
        $('.group_members').hide();
      }
    });
  });
</script>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Attach the click event to all buttons with the 'checkOutBtn' class
        document.querySelectorAll('.checkOutBtn').forEach(function(button) {
            button.addEventListener('click', function() {
                const entryId = button.getAttribute('data-entry-id');
                const roomId = button.getAttribute('data-room-id');
                const bedId = button.getAttribute('data-bed-id');
                const isFamily = button.getAttribute('data-is-family');
                      const groupmembers = button.getAttribute('data-group-members');
                // Send AJAX request to update checkout time
                fetch(`/Entry/checkout/${entryId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}', // Add CSRF token for security
                    },
                    body: JSON.stringify({
                        room_id: roomId,
                        bed_id: bedId,
                        isFamily:isFamily,
                        entryId:entryId,
                        groupmembers:groupmembers,
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload()
                    } else {
                        alert('Error while checking out!');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred!');
                });
            });
        });
    });
</script>
<script>
$(document).ready(function() {
    $('#entryForm').on('submit', function(e) {
        e.preventDefault(); // Prevent normal form submission

        let form = $(this);
        let formData = new FormData(this);

        $.ajax({
            url: form.attr('action'),
            method: form.attr('method'),
            data: formData,
            processData: false,
            contentType: false,
               success: function (response) {
                   // form.reset();
                // Check if the response indicates success
                if (response.success) {
                    toastr.success(response.message);  // Show success toast notification
                    // form.reset();  // Reset the form
                } else {
                    toastr.error(response.message);  // Show error toast notification
                }
            },
            error: function (xhr) {
                // Handle errors (e.g., validation errors)
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);  // Display validation error
                    });
                    toastr.error("Please correct the errors.");  // Show general error toast
                } else {
                    toastr.error("Something went wrong.");
                }
            }
        });
    });
});
</script>
<script>


    $('#roomForm').on('submit', function(e) {
        e.preventDefault();
        let form = $(this);
        let formData = form.serialize();

        $.ajax({
            type: 'POST',
            url: "{{ route('rooms.store') }}",
            data: formData,
           success: function (response) {
                // Check if the response indicates success
                if (response.success) {
                    toastr.success(response.message);  // Show success toast notification
                     form[0].reset();
                } else {
                    toastr.error(response.message);  // Show error toast notification
                }
            },
            error: function (xhr) {
                // Handle errors (e.g., validation errors)
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);  // Display validation error
                    });
                    toastr.error("Please correct the errors.");  // Show general error toast
                } else {
                    toastr.error("Something went wrong.");
                }
            }
        });
    });
</script>
<script type="text/javascript">
  $(document).ready(function () {
    $('#bedForm').on('submit', function (e) {
        e.preventDefault();

        let form = $(this);
        let url = form.attr('action');

        $.ajax({
            type: 'POST',
            url: url,
            data: form.serialize(),
               success: function (response) {
                // Check if the response indicates success
                if (response.success) {
                    toastr.success(response.message);  // Show success toast notification
                     form[0].reset();
                } else {
                    toastr.error(response.message);  // Show error toast notification
                }
            },
            error: function (xhr) {
                // Handle errors (e.g., validation errors)
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $(`[name="${key}"]`).after(`<div class="text-danger">${value[0]}</div>`);  // Display validation error
                    });
                    toastr.error("Please correct the errors.");  // Show general error toast
                } else {
                    toastr.error("Something went wrong.");
                }
            }
        });
    });
});

</script>


 <script>

    let currentPage = 1;
    const rowsPerPage = 10;
      function searchData() {
        const from = document.getElementById('from').value;
        const to = document.getElementById('to').value;
        const city = document.getElementById('fcity').value;
        const province = document.getElementById('fprovince').value;
        const month = document.getElementById('month').value;

        // Make an AJAX request to the backend to fetch filtered data
        fetch(`/filterrecords?from=${from}&to=${to}&city=${city}&province=${province}&month=${month}`)
          .then(response => response.json())
          .then(filteredData => {
            currentPage = 1; // Reset to page 1 on new search
            displayTable(filteredData);
          })
          .catch(error => console.error('Error fetching filtered data:', error));
      }


    function displayTable(data) {
      const tableBody = document.querySelector('#reportTable tbody');
      tableBody.innerHTML = '';

      const startIndex = (currentPage - 1) * rowsPerPage;
      const endIndex = startIndex + rowsPerPage;
      const paginatedData = data.slice(startIndex, endIndex);

      paginatedData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${item.name}</td>
          <td>${item.father_name}</td>
          <td>${item.cnic_no}</td>
          <td>${item.mobile_no}</td>
          <td>${item.cast}</td>
          <td>${item.profession}</td>
          <td>${item.province_name}</td>
          <td>${item.city_name}</td>
        `;
        tableBody.appendChild(row);
      });

      // Disable/Enable pagination buttons based on current page
      const totalPages = Math.ceil(data.length / rowsPerPage);
      const prevButton = document.querySelector('.pagination button:nth-child(1)');
      const nextButton = document.querySelector('.pagination button:nth-child(2)');

      prevButton.disabled = currentPage === 1;
      nextButton.disabled = currentPage === totalPages;
    }

    function changePage(direction) {
      currentPage += direction;
      searchData();
    }

    // Initially display all dummy data
    searchData();
  </script>







  <script>
  var swiper = new Swiper(".mySwiper", {
    slidesPerView: 1,
    spaceBetween: 30,
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    breakpoints: {
      768: {
        slidesPerView: 2,
      },
      992: {
        slidesPerView: 3,
      },
    },
  });
</script>
<!-- Charts are now handled by app.js -->
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
  $(document).ready(function () {
    $('#entriesTable').DataTable();
  });
</script>
 <script>
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.unique-rowCheckbox');

    selectAll.addEventListener('change', function() {
      checkboxes.forEach(cb => cb.checked = this.checked);
    });

    function sendMessage() {
      const message = document.getElementById('message').value;
      if(message.trim() === '') {
        alert('Please type a message.');
        return;
      }
      alert('Message sent: ' + message);
      document.getElementById('message').value = '';
    }
  </script>

<script>
  const video = document.getElementById('video');
  const canvas = document.getElementById('canvas');
  const captureBtn = document.getElementById('capture_btn');
  const capturedInput = document.getElementById('captured_image_input');

  // Start video stream
  navigator.mediaDevices.getUserMedia({ video: true })
    .then(stream => {
      video.srcObject = stream;
    })
    .catch(err => {
      console.error("Error accessing camera: ", err);
    });

  captureBtn.addEventListener('click', () => {
    const context = canvas.getContext('2d');
    canvas.style.display = 'block';
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to base64 image
    const imageData = canvas.toDataURL('image/png');
    capturedInput.value = imageData;
  });
</script>
<!-- Delete functions are now handled by app.js -->
<!-- Family checkbox functionality will be handled by app.js -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('search-number');
    const tableRows = document.querySelectorAll('.table-rowentry');

    searchInput.addEventListener('input', function () {
        const query = this.value.toLowerCase();

        tableRows.forEach(row => {
            const cnic = row.querySelector('[data-label="CNIC"]').textContent.toLowerCase();
            const mobile = row.querySelector('[data-label="MOBILE"]').textContent.toLowerCase();

            if (cnic.includes(query) || mobile.includes(query)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});
</script>
<!-- Room update functionality is handled by app.js -->

<script>
  // Export CSV
  $('#exportCSV').on('click', function () {
    let csv = [];
    $('#reportTable tr').each(function () {
      let row = [];
      $(this).find('th, td').each(function () {
        row.push('"' + $(this).text().replace(/"/g, '""') + '"');
      });
      csv.push(row.join(','));
    });

    let csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');
    let link = document.createElement("a");
    link.setAttribute("href", csvContent);
    link.setAttribute("download", "report.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // Export Excel
  $('#exportExcel').on('click', function () {
    let table = document.getElementById('reportTable').outerHTML;
    let blob = new Blob(['\ufeff' + table], { type: 'application/vnd.ms-excel' });
    let link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'report.xls';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // Export PDF
  $('#exportPDF').on('click', function () {
    const element = document.getElementById('tableWrapper');
    html2pdf().from(element).save('report.pdf');
  });

  // Print Table
  $('#printTable').on('click', function () {
    let printContents = document.getElementById('reportTable').innerHTML;
    let w = window.open();
    w.document.write('<html><head><title>Print Table</title></head><body>' + printContents + '</body></html>');
    w.document.close();
    w.print();
  });
</script>


<script>
  document.getElementById("cnic").addEventListener("input", function (e) {
    let value = e.target.value.replace(/[^0-9]/g, '').slice(0,13); // Only numbers, max 13 digits
    let formatted = "";
  console.log("asdasdasd");
    if (value.length > 5) {
      formatted += value.substring(0, 5) + "-";
      if (value.length > 12) {
        formatted += value.substring(5, 12) + "-" + value.substring(12);
      } else if (value.length > 5) {
        formatted += value.substring(5, 12);
      }
    } else {
      formatted = value;
    }

    e.target.value = formatted;
  });



    document.getElementById("mobile").addEventListener("input", function (e) {
    let value = e.target.value.replace(/[^0-9]/g, '').slice(0,11); // Only 11 digits max
    let formatted = "";

    if (value.length > 4) {
      formatted = value.substring(0, 4) + "-" + value.substring(4);
    } else {
      formatted = value;
    }

    e.target.value = formatted;
  });
</script>
</body>
</html>

















