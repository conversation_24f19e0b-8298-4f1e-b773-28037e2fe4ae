<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reports - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="#" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

   <div id="reports" class="page-section">
           <div class="container">
              <h1>Reports</h1>

              <div class="row">
                <div class="field">
                  <label for="from">From</label>
                  <input type="date" class="form-control" id="from">
                </div>

                <div class="field">
                  <label for="to">To</label>
                  <input type="date" class="form-control" id="to">
                </div>

                <div class="field">
                  <label for="city">City</label>
                     <select id="fcity" class="form-select">
                    <option value="">--Select City--</option>
                    <!-- Cities will be loaded dynamically -->
                  </select>
                </div>

                <div class="field">
                  <label for="province">Province</label>
                    <select id="fprovince" class="form-select">
                    <option value="">--Select Province--</option>
                    <!-- Provinces will be loaded dynamically -->
                  </select>
                </div>

                <div class="field">
                  <label for="month">Select Month</label>
                  <select id="month" class="form-select">
                    <option value="">--Select Month--</option>
                    <option value="January">January</option>
                    <option value="February">February</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="August">August</option>
                    <option value="September">September</option>
                    <option value="October">October</option>
                    <option value="November">November</option>
                    <option value="December">December</option>
                  </select>
                </div>
              </div>

              <button class="submit-btn" onclick="searchData()">Search</button>

              <!-- Responsive Table Wrapper -->
              <div class="table-wrapper" id="tableWrapper">
                  <button id="exportCSV" class="btn btn-primary"><i class="fas fa-file-csv"></i></button>
                <button id="exportExcel" class="btn btn-primary"><i class="fas fa-file-excel"></i></button>
                <button id="exportPDF" class="btn btn-primary"><i class="fas fa-file-pdf"></i></button>


                <table id="reportTable">
                  <thead>
                    <tr>
                      <th>Full Name</th>
                      <th>Father Name</th>
                      <th>Cnic</th>
                      <th>Mobile Numer</th>
                      <th>Cast</th>
                      <th>Profession</th>
                      <th>Province</th>
                      <th>City</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be populated dynamically after search -->
                  </tbody>
                </table>
              </div>

              <!-- Pagination Controls -->


            </div>
              <div class="pagination" id="pagination">
                <button onclick="changePage(-1)">Previous</button>
                <button onclick="changePage(1)">Next</button>
              </div>

          </div>


    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    let currentPage = 1;
    let itemsPerPage = 10;
    let filteredResults = [];

    // Initialize the reports page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadFilters();

      // Setup export buttons
      document.getElementById('exportCSV').addEventListener('click', exportToCSV);
      document.getElementById('exportExcel').addEventListener('click', exportToExcel);
      document.getElementById('exportPDF').addEventListener('click', exportToPDF);
    });

    function loadFilters() {
      const cities = dataManager.getAll('cities');
      const provinces = dataManager.getAll('provinces');

      const citySelect = document.getElementById('fcity');
      const provinceSelect = document.getElementById('fprovince');

      if (citySelect) {
        citySelect.innerHTML = '<option value="">--Select City--</option>';
        cities.forEach(city => {
          const option = document.createElement('option');
          option.value = city.id;
          option.textContent = city.city_name;
          citySelect.appendChild(option);
        });
      }

      if (provinceSelect) {
        provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
        provinces.forEach(province => {
          const option = document.createElement('option');
          option.value = province.id;
          option.textContent = province.province_name;
          provinceSelect.appendChild(option);
        });
      }
    }

    function searchData() {
      const fromDate = document.getElementById('from').value;
      const toDate = document.getElementById('to').value;
      const cityId = document.getElementById('fcity').value;
      const provinceId = document.getElementById('fprovince').value;
      const month = document.getElementById('month').value;

      let entries = dataManager.getAll('entries');

      // Filter by date range
      if (fromDate && toDate) {
        entries = entries.filter(entry => {
          const entryDate = new Date(entry.created_at);
          return entryDate >= new Date(fromDate) && entryDate <= new Date(toDate);
        });
      }

      // Filter by city
      if (cityId) {
        const city = dataManager.getById('cities', cityId);
        if (city) {
          entries = entries.filter(entry => entry.city_name === city.city_name);
        }
      }

      // Filter by province
      if (provinceId) {
        const province = dataManager.getById('provinces', provinceId);
        if (province) {
          entries = entries.filter(entry => entry.province_name === province.province_name);
        }
      }

      // Filter by month
      if (month) {
        entries = entries.filter(entry => {
          const entryMonth = new Date(entry.created_at).toLocaleString('default', { month: 'long' });
          return entryMonth === month;
        });
      }

      filteredResults = entries;
      currentPage = 1;
      displayResults();
    }

    function displayResults() {
      const tableBody = document.querySelector('#reportTable tbody');
      if (!tableBody) return;

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageData = filteredResults.slice(startIndex, endIndex);

      tableBody.innerHTML = '';
      pageData.forEach(entry => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${entry.name}</td>
          <td>${entry.father_name}</td>
          <td>${entry.cnic_no}</td>
          <td>${entry.mobile_no}</td>
          <td>${entry.cast}</td>
          <td>${entry.profession}</td>
          <td>${entry.province_name}</td>
          <td>${entry.city_name}</td>
        `;
        tableBody.appendChild(row);
      });

      updatePagination();
    }

    function updatePagination() {
      const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
      const pagination = document.getElementById('pagination');

      if (pagination) {
        pagination.innerHTML = `
          <button onclick="changePage(-1)" ${currentPage === 1 ? 'disabled' : ''}>Previous</button>
          <span>Page ${currentPage} of ${totalPages}</span>
          <button onclick="changePage(1)" ${currentPage === totalPages ? 'disabled' : ''}>Next</button>
        `;
      }
    }

    function changePage(direction) {
      const totalPages = Math.ceil(filteredResults.length / itemsPerPage);

      if (direction === -1 && currentPage > 1) {
        currentPage--;
      } else if (direction === 1 && currentPage < totalPages) {
        currentPage++;
      }

      displayResults();
    }

    function exportToCSV() {
      if (filteredResults.length === 0) {
        alert('No data to export. Please search first.');
        return;
      }

      const headers = ['Full Name', 'Father Name', 'CNIC', 'Mobile Number', 'Cast', 'Profession', 'Province', 'City'];
      const csvContent = [
        headers.join(','),
        ...filteredResults.map(entry => [
          entry.name,
          entry.father_name,
          entry.cnic_no,
          entry.mobile_no,
          entry.cast,
          entry.profession,
          entry.province_name,
          entry.city_name
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `darga_report_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }

    function exportToExcel() {
      alert('Excel export functionality will be implemented.');
    }

    function exportToPDF() {
      alert('PDF export functionality will be implemented.');
    }
  </script>
</body>
</html>
