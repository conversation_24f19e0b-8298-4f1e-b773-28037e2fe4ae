<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reports - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
  <style>
    .field {
      margin-bottom: 1rem;
      flex: 1;
      margin-right: 1rem;
    }

    .field:last-child {
      margin-right: 0;
    }

    .row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .submit-btn {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      border: none;
      padding: 0.75rem 2rem;
      border-radius: 8px;
      font-weight: bold;
      cursor: pointer;
      margin-bottom: 2rem;
      transition: all 0.3s ease;
    }

    .submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }

    .table-wrapper {
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      padding: 1.5rem;
      margin-bottom: 2rem;
      overflow-x: auto;
    }

    #reportTable {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    #reportTable th {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 12px;
      text-align: left;
      font-weight: bold;
      border: none;
    }

    #reportTable td {
      padding: 12px;
      border-bottom: 1px solid #eee;
      transition: background-color 0.3s ease;
    }

    #reportTable tr:hover {
      background-color: #f8f9fa;
    }

    #reportTable tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      margin: 2rem 0;
    }

    .pagination button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .pagination button:hover:not(:disabled) {
      background: #45a049;
      transform: translateY(-1px);
    }

    .pagination button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .pagination span {
      font-weight: bold;
      color: #333;
    }

    .export-buttons {
      margin-bottom: 1rem;
    }

    .export-buttons button {
      margin-right: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      border: none;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    #exportCSV {
      background: #28a745;
    }

    #exportExcel {
      background: #17a2b8;
    }

    #exportPDF {
      background: #dc3545;
    }

    .export-buttons button:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    }

    .form-control, .form-select {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 0.75rem;
      transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
      outline: none;
    }

    .container h1 {
      color: #4CAF50;
      font-weight: bold;
      margin-bottom: 2rem;
      text-align: center;
    }

    @media (max-width: 768px) {
      .row {
        flex-direction: column;
      }

      .field {
        margin-right: 0;
      }

      .table-wrapper {
        padding: 1rem;
      }

      #reportTable {
        font-size: 0.9rem;
      }

      #reportTable th,
      #reportTable td {
        padding: 8px;
      }
    }
  </style>
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="#" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-files me-2"></i>Reports & Analytics</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button class="btn btn-success" onclick="exportToCSV()">
                <i class="bi bi-download me-2"></i>Export CSV
              </button>
            </div>
          </div>

          <!-- Main Content -->
          <div class="container">
            <h1>Reports</h1>

            <div class="row">
              <div class="field">
                <label for="from">From</label>
                <input type="date" class="form-control" id="from">
              </div>

              <div class="field">
                <label for="to">To</label>
                <input type="date" class="form-control" id="to">
              </div>

              <div class="field">
                <label for="city">City</label>
                <select id="fcity" class="form-select">
                  <option value="">--Select City--</option>
                  <!-- Cities will be loaded dynamically -->
                </select>
              </div>

              <div class="field">
                <label for="province">Province</label>
                <select id="fprovince" class="form-select">
                  <option value="">--Select Province--</option>
                  <!-- Provinces will be loaded dynamically -->
                </select>
              </div>

              <div class="field">
                <label for="month">Select Month</label>
                <select id="month" class="form-select">
                  <option value="">--Select Month--</option>
                  <option value="January">January</option>
                  <option value="February">February</option>
                  <option value="March">March</option>
                  <option value="April">April</option>
                  <option value="May">May</option>
                  <option value="June">June</option>
                  <option value="July">July</option>
                  <option value="August">August</option>
                  <option value="September">September</option>
                  <option value="October">October</option>
                  <option value="November">November</option>
                  <option value="December">December</option>
                </select>
              </div>
            </div>

            <button class="submit-btn" onclick="searchData()">Search</button>

            <!-- Responsive Table Wrapper -->
            <div class="table-wrapper" id="tableWrapper">
              <div class="export-buttons">
                <button id="exportCSV"><i class="fas fa-file-csv me-2"></i>Export CSV</button>
                <button id="exportExcel"><i class="fas fa-file-excel me-2"></i>Export Excel</button>
                <button id="exportPDF"><i class="fas fa-file-pdf me-2"></i>Export PDF</button>
              </div>

              <table id="reportTable">
                <thead>
                  <tr>
                    <th>Full Name</th>
                    <th>Father Name</th>
                    <th>Cnic</th>
                    <th>Mobile Number</th>
                    <th>Cast</th>
                    <th>Profession</th>
                    <th>Province</th>
                    <th>City</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Data will be populated dynamically after search -->
                </tbody>
              </table>
            </div>

            <!-- Pagination Controls -->
            <div class="pagination" id="pagination">
              <button onclick="changePage(-1)">Previous</button>
              <button onclick="changePage(1)">Next</button>
            </div>

            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    let currentPage = 1;
    let itemsPerPage = 10;
    let filteredResults = [];

    // Initialize the reports page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadFilters();
      loadDummyData();

      // Initialize pagination display
      updatePagination();

      // Setup export buttons
      document.getElementById('exportCSV').addEventListener('click', exportToCSV);
      document.getElementById('exportExcel').addEventListener('click', exportToExcel);
      document.getElementById('exportPDF').addEventListener('click', exportToPDF);
    });

    function loadDummyData() {
      // Add dummy provinces if none exist
      const existingProvinces = dataManager.getAll('provinces');
      if (existingProvinces.length === 0) {
        const dummyProvinces = [
          { id: 1, province_name: "Punjab" },
          { id: 2, province_name: "Sindh" },
          { id: 3, province_name: "KPK" },
          { id: 4, province_name: "Balochistan" }
        ];
        dummyProvinces.forEach(province => {
          dataManager.add('provinces', province);
        });
      }

      // Add dummy cities if none exist
      const existingCities = dataManager.getAll('cities');
      if (existingCities.length === 0) {
        const dummyCities = [
          { id: 1, city_name: "Lahore", province_id: 1, province_name: "Punjab" },
          { id: 2, city_name: "Faisalabad", province_id: 1, province_name: "Punjab" },
          { id: 3, city_name: "Karachi", province_id: 2, province_name: "Sindh" },
          { id: 4, city_name: "Hyderabad", province_id: 2, province_name: "Sindh" },
          { id: 5, city_name: "Peshawar", province_id: 3, province_name: "KPK" },
          { id: 6, city_name: "Islamabad", province_id: 3, province_name: "KPK" },
          { id: 7, city_name: "Quetta", province_id: 4, province_name: "Balochistan" }
        ];
        dummyCities.forEach(city => {
          dataManager.add('cities', city);
        });
      }

      // Add dummy data if no entries exist
      const existingEntries = dataManager.getAll('entries');
      if (existingEntries.length === 0) {
        const dummyEntries = [
          {
            id: 1,
            name: "Ahmed Ali Khan",
            father_name: "Muhammad Ali Khan",
            cnic_no: "42101-1234567-1",
            mobile_no: "0300-1234567",
            cast: "Rajput",
            profession: "Engineer",
            province_name: "Punjab",
            city_name: "Lahore",
            created_at: "2024-01-15T10:30:00",
            status: "checked_in"
          },
          {
            id: 2,
            name: "Fatima Sheikh",
            father_name: "Abdul Rahman Sheikh",
            cnic_no: "42201-2345678-2",
            mobile_no: "0301-2345678",
            cast: "Sheikh",
            profession: "Doctor",
            province_name: "Punjab",
            city_name: "Karachi",
            created_at: "2024-01-16T14:20:00",
            status: "checked_out"
          },
          {
            id: 3,
            name: "Hassan Mahmood",
            father_name: "Mahmood Ahmad",
            cnic_no: "42301-3456789-3",
            mobile_no: "0302-3456789",
            cast: "Arain",
            profession: "Teacher",
            province_name: "Sindh",
            city_name: "Hyderabad",
            created_at: "2024-01-17T09:15:00",
            status: "checked_in"
          },
          {
            id: 4,
            name: "Ayesha Malik",
            father_name: "Tariq Malik",
            cnic_no: "42401-4567890-4",
            mobile_no: "0303-4567890",
            cast: "Malik",
            profession: "Lawyer",
            province_name: "KPK",
            city_name: "Peshawar",
            created_at: "2024-01-18T16:45:00",
            status: "checked_in"
          },
          {
            id: 5,
            name: "Omar Farooq",
            father_name: "Farooq Ahmed",
            cnic_no: "42501-5678901-5",
            mobile_no: "0304-5678901",
            cast: "Syed",
            profession: "Businessman",
            province_name: "Balochistan",
            city_name: "Quetta",
            created_at: "2024-01-19T11:30:00",
            status: "checked_out"
          },
          {
            id: 6,
            name: "Zainab Hussain",
            father_name: "Hussain Ali",
            cnic_no: "42601-6789012-6",
            mobile_no: "0305-6789012",
            cast: "Hussain",
            profession: "Nurse",
            province_name: "Punjab",
            city_name: "Faisalabad",
            created_at: "2024-01-20T13:20:00",
            status: "checked_in"
          },
          {
            id: 7,
            name: "Bilal Qureshi",
            father_name: "Qureshi Sahib",
            cnic_no: "42701-7890123-7",
            mobile_no: "0306-7890123",
            cast: "Qureshi",
            profession: "Accountant",
            province_name: "Sindh",
            city_name: "Karachi",
            created_at: "2024-01-21T08:45:00",
            status: "checked_in"
          },
          {
            id: 8,
            name: "Mariam Khan",
            father_name: "Khan Sahib",
            cnic_no: "42801-8901234-8",
            mobile_no: "0307-8901234",
            cast: "Khan",
            profession: "Designer",
            province_name: "KPK",
            city_name: "Islamabad",
            created_at: "2024-01-22T15:10:00",
            status: "checked_out"
          }
        ];

        // Add dummy entries to localStorage
        dummyEntries.forEach(entry => {
          dataManager.add('entries', entry);
        });
      }

      // Load all entries for initial display
      filteredResults = dataManager.getAll('entries');
      displayResults();
    }

    function loadFilters() {
      const cities = dataManager.getAll('cities');
      const provinces = dataManager.getAll('provinces');

      const citySelect = document.getElementById('fcity');
      const provinceSelect = document.getElementById('fprovince');

      if (citySelect) {
        citySelect.innerHTML = '<option value="">--Select City--</option>';
        cities.forEach(city => {
          const option = document.createElement('option');
          option.value = city.id;
          option.textContent = city.city_name;
          citySelect.appendChild(option);
        });
      }

      if (provinceSelect) {
        provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
        provinces.forEach(province => {
          const option = document.createElement('option');
          option.value = province.id;
          option.textContent = province.province_name;
          provinceSelect.appendChild(option);
        });
      }
    }

    function searchData() {
      const fromDate = document.getElementById('from').value;
      const toDate = document.getElementById('to').value;
      const cityId = document.getElementById('fcity').value;
      const provinceId = document.getElementById('fprovince').value;
      const month = document.getElementById('month').value;

      let entries = dataManager.getAll('entries');

      // Filter by date range
      if (fromDate && toDate) {
        entries = entries.filter(entry => {
          const entryDate = new Date(entry.created_at);
          return entryDate >= new Date(fromDate) && entryDate <= new Date(toDate);
        });
      }

      // Filter by city
      if (cityId) {
        const city = dataManager.getById('cities', cityId);
        if (city) {
          entries = entries.filter(entry => entry.city_name === city.city_name);
        }
      }

      // Filter by province
      if (provinceId) {
        const province = dataManager.getById('provinces', provinceId);
        if (province) {
          entries = entries.filter(entry => entry.province_name === province.province_name);
        }
      }

      // Filter by month
      if (month) {
        entries = entries.filter(entry => {
          const entryMonth = new Date(entry.created_at).toLocaleString('default', { month: 'long' });
          return entryMonth === month;
        });
      }

      filteredResults = entries;
      currentPage = 1;
      displayResults();
    }

    function displayResults() {
      const tableBody = document.querySelector('#reportTable tbody');
      if (!tableBody) return;

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageData = filteredResults.slice(startIndex, endIndex);

      tableBody.innerHTML = '';
      pageData.forEach(entry => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${entry.name}</td>
          <td>${entry.father_name}</td>
          <td>${entry.cnic_no}</td>
          <td>${entry.mobile_no}</td>
          <td>${entry.cast}</td>
          <td>${entry.profession}</td>
          <td>${entry.province_name}</td>
          <td>${entry.city_name}</td>
        `;
        tableBody.appendChild(row);
      });

      updatePagination();
    }

    function updatePagination() {
      const totalPages = Math.ceil(filteredResults.length / itemsPerPage) || 1;
      const pagination = document.getElementById('pagination');

      if (pagination) {
        if (filteredResults.length === 0) {
          pagination.innerHTML = `
            <button onclick="changePage(-1)" disabled>Previous</button>
            <span>No results found</span>
            <button onclick="changePage(1)" disabled>Next</button>
          `;
        } else {
          pagination.innerHTML = `
            <button onclick="changePage(-1)" ${currentPage === 1 ? 'disabled' : ''}>Previous</button>
            <span>Page ${currentPage} of ${totalPages} (${filteredResults.length} results)</span>
            <button onclick="changePage(1)" ${currentPage === totalPages ? 'disabled' : ''}>Next</button>
          `;
        }
      }
    }

    function changePage(direction) {
      const totalPages = Math.ceil(filteredResults.length / itemsPerPage);

      if (direction === -1 && currentPage > 1) {
        currentPage--;
      } else if (direction === 1 && currentPage < totalPages) {
        currentPage++;
      }

      displayResults();
    }

    function exportToCSV() {
      if (filteredResults.length === 0) {
        alert('No data to export. Please search first.');
        return;
      }

      const headers = ['Full Name', 'Father Name', 'CNIC', 'Mobile Number', 'Cast', 'Profession', 'Province', 'City'];
      const csvContent = [
        headers.join(','),
        ...filteredResults.map(entry => [
          entry.name,
          entry.father_name,
          entry.cnic_no,
          entry.mobile_no,
          entry.cast,
          entry.profession,
          entry.province_name,
          entry.city_name
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `darga_report_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }

    function exportToExcel() {
      alert('Excel export functionality will be implemented.');
    }

    function exportToPDF() {
      alert('PDF export functionality will be implemented.');
    }
  </script>
</body>
</html>
