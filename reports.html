<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reports - Darga Management</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" href="/style.css">
</head>

<body>
  <!-- Company bar -->
  <div class="company-navbar">
    <div class="company-info">
      <span id="currentUser">Admin User</span>
    </div>
    <div class="page-info">
      <span style="margin-right: 40px;" id="currentDateTime"></span>
      <span><a href="#" onclick="logout()" class="dropdown-item">Logout</a></span>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container-fluid">
    <button class="btn btn-toggle-sidebar" type="button" onclick="toggleSidebar()" aria-label="Toggle sidebar">☰</button>
    <div class="row">
      <nav id="sidebarMenu" class="sidebar" aria-label="Main navigation">
        <button class="close-btn" onclick="toggleSidebar()" aria-label="Close sidebar">×</button>
        <div class="position-sticky">
          <div class="sidebar-header">
            <img src="/images/alexander-hipp-iEEBWgY_6lA-unsplash.jpg" alt="User Profile">
            <div class="user-info">
              <div class="user-name" id="sidebarUserName">Admin User</div>
              <a href="#" class="settings-link"><i class="bi bi-gear-fill"></i> Setting</a>
            </div>
          </div>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="index.html"><i class="bi bi-house"></i> Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#setupDropdown" aria-expanded="false" aria-controls="setupDropdown">
                <i class="bi bi-gear"></i> Setup <span class="arrow">▼</span>
              </a>
              <div id="setupDropdown" class="collapse">
                <a class="nav-link ms-3" href="users.html">User</a>
                <a class="nav-link ms-3" href="provinces.html">Province</a>
                <a class="nav-link ms-3" href="cities.html">City</a>
              </div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="add-entry.html"><i class="bi bi-plus"></i> Add Entry</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="entries.html"><i class="bi bi-list"></i> View Entries</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="rooms.html"><i class="bi bi-door-open"></i> Rooms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="reports.html"><i class="bi bi-files"></i> Reports</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="messages.html"><i class="bi bi-chat"></i> Messages</a>
            </li>
          </ul>
        </div>
      </nav>
      
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          <!-- Page Header -->
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2"><i class="bi bi-files me-2"></i>Reports & Analytics</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <button class="btn btn-success" onclick="exportToCSV()">
                <i class="bi bi-download me-2"></i>Export CSV
              </button>
            </div>
          </div>

          <!-- Filters Section -->
          <div class="container">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Filter Options</h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-3">
                    <label for="from" class="form-label">From Date</label>
                    <input type="date" id="from" class="form-control">
                  </div>
                  <div class="col-md-3">
                    <label for="to" class="form-label">To Date</label>
                    <input type="date" id="to" class="form-control">
                  </div>
                  <div class="col-md-3">
                    <label for="fcity" class="form-label">City</label>
                    <select id="fcity" class="form-select">
                      <option value="">--Select City--</option>
                      <!-- Cities will be loaded dynamically -->
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="fprovince" class="form-label">Province</label>
                    <select id="fprovince" class="form-select">
                      <option value="">--Select Province--</option>
                      <!-- Provinces will be loaded dynamically -->
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="month" class="form-label">Month</label>
                    <select id="month" class="form-select">
                      <option value="">--Select Month--</option>
                      <option value="January">January</option>
                      <option value="February">February</option>
                      <option value="March">March</option>
                      <option value="April">April</option>
                      <option value="May">May</option>
                      <option value="June">June</option>
                      <option value="July">July</option>
                      <option value="August">August</option>
                      <option value="September">September</option>
                      <option value="October">October</option>
                      <option value="November">November</option>
                      <option value="December">December</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select id="status" class="form-select">
                      <option value="">--All Status--</option>
                      <option value="checked_in">Checked In</option>
                      <option value="checked_out">Checked Out</option>
                    </select>
                  </div>
                  <div class="col-md-6 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="searchData()">
                      <i class="bi bi-search me-2"></i>Search
                    </button>
                    <button class="btn btn-secondary" onclick="clearFilters()">
                      <i class="bi bi-x-circle me-2"></i>Clear
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="card text-white bg-primary">
                  <div class="card-body">
                    <h5 class="card-title">Total Entries</h5>
                    <h2 id="totalEntries">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-white bg-success">
                  <div class="card-body">
                    <h5 class="card-title">Checked In</h5>
                    <h2 id="checkedIn">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-white bg-warning">
                  <div class="card-body">
                    <h5 class="card-title">Checked Out</h5>
                    <h2 id="checkedOut">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-white bg-info">
                  <div class="card-body">
                    <h5 class="card-title">Family Groups</h5>
                    <h2 id="familyGroups">0</h2>
                  </div>
                </div>
              </div>
            </div>

            <!-- Results Table -->
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>Report Results</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped" id="reportTable">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Father Name</th>
                        <th>CNIC</th>
                        <th>Mobile</th>
                        <th>Cast</th>
                        <th>Profession</th>
                        <th>Province</th>
                        <th>City</th>
                        <th>Check In</th>
                        <th>Check Out</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- Results will be loaded here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            
            <div class="footer mt-4">
              Copyright © 2025 Xenith. All rights reserved.
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
  <script>
    let filteredResults = [];

    // Initialize the reports page
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      setInterval(updateDateTime, 1000);
      loadFilters();
      loadAllData();
    });

    function loadFilters() {
      const cities = dataManager.getAll('cities');
      const provinces = dataManager.getAll('provinces');
      
      const citySelect = document.getElementById('fcity');
      const provinceSelect = document.getElementById('fprovince');
      
      if (citySelect) {
        citySelect.innerHTML = '<option value="">--Select City--</option>';
        cities.forEach(city => {
          const option = document.createElement('option');
          option.value = city.id;
          option.textContent = city.city_name;
          citySelect.appendChild(option);
        });
      }
      
      if (provinceSelect) {
        provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
        provinces.forEach(province => {
          const option = document.createElement('option');
          option.value = province.id;
          option.textContent = province.province_name;
          provinceSelect.appendChild(option);
        });
      }
    }

    function loadAllData() {
      const entries = dataManager.getAll('entries');
      filteredResults = entries;
      updateStatistics(entries);
      displayResults(entries);
    }

    function searchData() {
      const fromDate = document.getElementById('from').value;
      const toDate = document.getElementById('to').value;
      const cityId = document.getElementById('fcity').value;
      const provinceId = document.getElementById('fprovince').value;
      const month = document.getElementById('month').value;
      const status = document.getElementById('status').value;
      
      let entries = dataManager.getAll('entries');
      
      // Filter by date range
      if (fromDate && toDate) {
        entries = entries.filter(entry => {
          const entryDate = new Date(entry.created_at);
          return entryDate >= new Date(fromDate) && entryDate <= new Date(toDate);
        });
      }
      
      // Filter by city
      if (cityId) {
        const city = dataManager.getById('cities', cityId);
        entries = entries.filter(entry => entry.city_name === city.city_name);
      }
      
      // Filter by province
      if (provinceId) {
        const province = dataManager.getById('provinces', provinceId);
        entries = entries.filter(entry => entry.province_name === province.province_name);
      }
      
      // Filter by month
      if (month) {
        entries = entries.filter(entry => {
          const entryMonth = new Date(entry.created_at).toLocaleString('default', { month: 'long' });
          return entryMonth === month;
        });
      }
      
      // Filter by status
      if (status) {
        entries = entries.filter(entry => entry.status === status);
      }
      
      filteredResults = entries;
      updateStatistics(entries);
      displayResults(entries);
    }

    function updateStatistics(entries) {
      const totalEntries = entries.length;
      const checkedIn = entries.filter(e => e.status === 'checked_in').length;
      const checkedOut = entries.filter(e => e.status === 'checked_out').length;
      const familyGroups = entries.filter(e => e.family).length;
      
      document.getElementById('totalEntries').textContent = totalEntries;
      document.getElementById('checkedIn').textContent = checkedIn;
      document.getElementById('checkedOut').textContent = checkedOut;
      document.getElementById('familyGroups').textContent = familyGroups;
    }

    function displayResults(entries) {
      const tableBody = document.querySelector('#reportTable tbody');
      if (tableBody) {
        tableBody.innerHTML = '';
        entries.forEach(entry => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${entry.name}</td>
            <td>${entry.father_name}</td>
            <td>${entry.cnic_no}</td>
            <td>${entry.mobile_no}</td>
            <td>${entry.cast}</td>
            <td>${entry.profession}</td>
            <td>${entry.province_name}</td>
            <td>${entry.city_name}</td>
            <td>${entry.check_in}</td>
            <td>${entry.check_out || 'Not checked out'}</td>
            <td>
              <span class="badge ${entry.status === 'checked_in' ? 'bg-success' : 'bg-secondary'}">
                ${entry.status}
              </span>
            </td>
          `;
          tableBody.appendChild(row);
        });
      }
    }

    function clearFilters() {
      document.getElementById('from').value = '';
      document.getElementById('to').value = '';
      document.getElementById('fcity').value = '';
      document.getElementById('fprovince').value = '';
      document.getElementById('month').value = '';
      document.getElementById('status').value = '';
      loadAllData();
    }

    function exportToCSV() {
      if (filteredResults.length === 0) {
        alert('No data to export');
        return;
      }
      
      const headers = ['Name', 'Father Name', 'CNIC', 'Mobile', 'Cast', 'Profession', 'Province', 'City', 'Check In', 'Check Out', 'Status'];
      const csvContent = [
        headers.join(','),
        ...filteredResults.map(entry => [
          entry.name,
          entry.father_name,
          entry.cnic_no,
          entry.mobile_no,
          entry.cast,
          entry.profession,
          entry.province_name,
          entry.city_name,
          entry.check_in,
          entry.check_out || 'Not checked out',
          entry.status
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `darga_report_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  </script>
</body>
</html>
